{"version": 3, "file": "ExpoSQLite.web.js", "sourceRoot": "", "sources": ["../src/ExpoSQLite.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,MAAM,CAAC;AAE3C,IAAI,UAAU,CAAC;AAEf,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAClC,kDAAkD;IAClD,UAAU,GAAG,EAAE,CAAC;AAClB,CAAC;KAAM,IAAI,OAAO,UAAU,CAAC,cAAc,KAAK,WAAW,EAAE,CAAC;IAC5D,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC;AACjD,CAAC;KAAM,CAAC;IACN,UAAU,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC;AACtD,CAAC;AAED,eAAe,UAAU,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo';\n\nlet ExpoSQLite;\n\nif (typeof window === 'undefined') {\n  // expo-sqlite is not supported on server runtime.\n  ExpoSQLite = {};\n} else if (typeof globalThis.ExpoDomWebView !== 'undefined') {\n  ExpoSQLite = requireNativeModule('ExpoSQLite');\n} else {\n  ExpoSQLite = require('../web/SQLiteModule').default;\n}\n\nexport default ExpoSQLite;\n"]}