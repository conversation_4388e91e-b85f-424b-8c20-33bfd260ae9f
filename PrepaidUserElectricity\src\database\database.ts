import * as SQLite from 'expo-sqlite';

// Database configuration
const DATABASE_NAME = 'prepaid_electricity.db';
const DATABASE_VERSION = 1;

// Database instance
let db: SQLite.SQLiteDatabase | null = null;

// Initialize database
export const initDatabase = async (): Promise<SQLite.SQLiteDatabase> => {
  if (db) {
    return db;
  }

  try {
    db = await SQLite.openDatabaseAsync(DATABASE_NAME);
    await createTables();
    console.log('Database initialized successfully');
    return db;
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};

// Create all necessary tables
const createTables = async (): Promise<void> => {
  if (!db) {
    throw new Error('Database not initialized');
  }

  try {
    // Settings table
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Purchases table
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS purchases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        currency_amount REAL NOT NULL,
        unit_amount REAL NOT NULL,
        currency_type TEXT NOT NULL,
        unit_type TEXT NOT NULL,
        cost_per_unit REAL NOT NULL,
        purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        notes TEXT
      );
    `);

    // Usage records table
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS usage_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        previous_units REAL NOT NULL,
        current_units REAL NOT NULL,
        usage_amount REAL NOT NULL,
        unit_type TEXT NOT NULL,
        record_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        notes TEXT
      );
    `);

    // History table for comprehensive tracking
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL, -- 'purchase' or 'usage'
        reference_id INTEGER NOT NULL, -- ID from purchases or usage_records
        amount REAL NOT NULL,
        currency_value REAL,
        unit_type TEXT NOT NULL,
        currency_type TEXT,
        date DATETIME DEFAULT CURRENT_TIMESTAMP,
        description TEXT
      );
    `);

    // Totals tracking table
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS totals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        period_type TEXT NOT NULL, -- 'daily', 'weekly', 'monthly'
        period_start DATE NOT NULL,
        period_end DATE NOT NULL,
        total_purchases_currency REAL DEFAULT 0,
        total_purchases_units REAL DEFAULT 0,
        total_usage_units REAL DEFAULT 0,
        currency_type TEXT NOT NULL,
        unit_type TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    console.log('All tables created successfully');
  } catch (error) {
    console.error('Error creating tables:', error);
    throw error;
  }
};

// Get database instance
export const getDatabase = (): SQLite.SQLiteDatabase => {
  if (!db) {
    throw new Error('Database not initialized. Call initDatabase() first.');
  }
  return db;
};

// Close database connection
export const closeDatabase = async (): Promise<void> => {
  if (db) {
    await db.closeAsync();
    db = null;
    console.log('Database connection closed');
  }
};

// Reset database (for factory reset)
export const resetDatabase = async (): Promise<void> => {
  if (!db) {
    throw new Error('Database not initialized');
  }

  try {
    // Drop all tables
    await db.execAsync('DROP TABLE IF EXISTS settings;');
    await db.execAsync('DROP TABLE IF EXISTS purchases;');
    await db.execAsync('DROP TABLE IF EXISTS usage_records;');
    await db.execAsync('DROP TABLE IF EXISTS history;');
    await db.execAsync('DROP TABLE IF EXISTS totals;');

    // Recreate tables
    await createTables();
    console.log('Database reset successfully');
  } catch (error) {
    console.error('Error resetting database:', error);
    throw error;
  }
};

// Database utility functions
export const executeQuery = async (query: string, params: any[] = []): Promise<any> => {
  if (!db) {
    throw new Error('Database not initialized');
  }

  try {
    const result = await db.runAsync(query, params);
    return result;
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  }
};

export const fetchQuery = async (query: string, params: any[] = []): Promise<any[]> => {
  if (!db) {
    throw new Error('Database not initialized');
  }

  try {
    const result = await db.getAllAsync(query, params);
    return result;
  } catch (error) {
    console.error('Error fetching query:', error);
    throw error;
  }
};

export const fetchSingleQuery = async (query: string, params: any[] = []): Promise<any> => {
  if (!db) {
    throw new Error('Database not initialized');
  }

  try {
    const result = await db.getFirstAsync(query, params);
    return result;
  } catch (error) {
    console.error('Error fetching single query:', error);
    throw error;
  }
};
