var badgin=function(e){"use strict";let t=!1;const n={mediaQuery:null,value:0};function i(){return"ExperimentalBadge"in window&&!!window.ExperimentalBadge}function a(){return"setExperimentalAppBadge"in navigator&&!!navigator.setExperimentalAppBadge&&"clearExperimentalAppBadge"in navigator&&!!navigator.clearExperimentalAppBadge}function o(){return n.mediaQuery||(n.mediaQuery=window.matchMedia("(display-mode: standalone)"),n.mediaQuery.onchange=e=>{r(n.value)}),n.mediaQuery.matches&&(i()||a())}function r(e){return n.value=e,o()?i()?(window.ExperimentalBadge.set(e),!0):!!a()&&(navigator.setExperimentalAppBadge(e),!0):(t||"ExperimentalBadge"in window||"setExperimentalAppBadge"in navigator||(console.warn("Badging API must be enabled. Please check here how you can enable it: https://developers.google.com/web/updates/2018/12/badging-api#use"),t=!0),!1)}function l(e,t){for(const n of Object.keys(t))t[n]instanceof Object&&Object.assign(t[n],l(e[n],t[n]));return Object.assign(e||{},t),e}function c(e){return void 0!==e&&Number.isInteger(e)&&e>=0}const d={backgroundColor:"#424242",color:"#ffffff",indicator:"!",radius:3,size:7,horizontalMargin:0,verticalMargin:0,horizontalPadding:1,verticalPadding:1},s=navigator.userAgent.indexOf("Firefox")>-1,u=()=>{const e=document.head.getElementsByTagName("link"),t=[];for(let n=0;n<e.length;n++){const i=e[n],a=i.getAttribute("href"),o=i.getAttribute("rel");a&&(o&&-1!==o.split(" ").indexOf("icon")&&t.push(i))}return t},g=()=>{const e=u();let t=null,n=0;for(let i=0;i<e.length;i++){const a=e[i],o=a.getAttribute("href"),r=a.getAttribute("sizes");if(null==o?void 0:o.endsWith(".svg"))return a;if(!r){t||(t=a,n=0);continue}if("any"===r)return a;const l=parseInt(r.split("x")[0],10);Number.isNaN(l)?t||(t=a,n=0):l>n&&(t=a,n=l)}return t},v={favicons:null,bestFavicon:null,bestFaviconImage:null,value:0,options:d},f=window.matchMedia("screen and (min-resolution: 2dppx)"),m=()=>Math.ceil(window.devicePixelRatio)||1,p=()=>{E(v.value,v.options)},h=()=>16*m(),b=(e,t,n)=>{const i=h(),a=document.createElement("canvas");a.width=i,a.height=i;const o=a.getContext("2d");o&&(e.width=i,e.height=i,o.drawImage(e,0,0,e.width,e.height),w(o,t,n),(e=>{if(!e)return;for(const e of u())e.parentNode&&e.parentNode.removeChild(e);const t=document.createElement("link");t.id="badgin",t.type="image/x-icon",t.rel="icon favicon",t.href=e,document.getElementsByTagName("head")[0].appendChild(t)})(a.toDataURL()))},w=(e,t,n)=>{const i=m(),a=h();let o="";if(o=c(t)?0===t?"":t<100?String(t):"99+":n.indicator,!o)return;const r=n.size-2,l=n.size*i+"px Arial";e.font=l;const{width:d}=e.measureText(o);e.restore();const u=d+2*n.horizontalPadding,g=a-(r*i+2*n.verticalPadding)-n.verticalMargin,v=a-u-n.horizontalMargin,f=16*i-n.verticalMargin,p=16*i-n.horizontalMargin,b=n.radius;e.globalAlpha=1,e.fillStyle=n.backgroundColor,e.strokeStyle=n.backgroundColor,e.lineWidth=0,e.beginPath(),e.moveTo(v+b,g),e.quadraticCurveTo(v,g,v,g+b),e.lineTo(v,f-b),e.quadraticCurveTo(v,f,v+b,f),e.lineTo(p-b,f),e.quadraticCurveTo(p,f,p,f-b),e.lineTo(p,g+b),e.quadraticCurveTo(p,g,p-b,g),e.closePath(),e.fill(),e.save(),e.font=l,e.fillStyle=n.color,e.textAlign="center",e.textBaseline="hanging",e.fillText(o,v+u/2,g+n.verticalPadding+(s?1:0)),e.save()};function x(){return!!g()}function E(e,t){if(v.value=e,l(v.options,t||{}),!x())return!1;if(!v.bestFavicon){const e=g();if(e){const t=document.createElement("img");e.href.match(/^data/)||(t.crossOrigin="anonymous"),t.src=e.href,v.bestFavicon=e,v.bestFaviconImage=t}f.addEventListener("change",p)}return v.favicons||(v.favicons=u()),!!v.bestFaviconImage&&(v.bestFaviconImage.complete?(b(v.bestFaviconImage,v.value,v.options),!0):(v.bestFaviconImage.addEventListener("load",(function(){b(this,v.value,v.options)})),!0))}const y={title:null,value:0,options:{indicator:"!"}};function B(e,t){return null===y.title&&(y.title=document.title,Object.defineProperty(document,"title",{get:()=>y.title,set:e=>{y.title=e,function(e,t,n){let i=e;i=c(t)?0===t?e:`(${t}) ${e}`:`(${n.indicator}) ${e}`;const a=document.querySelector("title");a&&(a.childNodes[0].nodeValue=i)}(y.title,y.value,y.options)}})),y.value=e,l(y.options,t||{}),document.title=document.title,!0}return e.clear=function(){o()&&(i()?window.ExperimentalBadge.clear():a()&&navigator.clearExperimentalAppBadge()),function(){if(x()&&(v.value=0,v.options=d,f.removeEventListener("change",p),v.favicons)){for(const e of u())e.parentNode&&e.parentNode.removeChild(e);for(const e of v.favicons)document.head.appendChild(e);v.favicons=null,v.bestFavicon=null,v.bestFaviconImage=null}}(),y.value=0,document.title=document.title},e.set=function(e,t={}){switch(t.method){case void 0:case"Badging":if(r(e)&&"Badging"===t.method)break;case"Favicon":if(E(e,t.favicon))break;default:B(e,t.title)}},e}({});
