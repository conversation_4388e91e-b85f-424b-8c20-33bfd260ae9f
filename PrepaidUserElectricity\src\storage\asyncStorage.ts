import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppConfig, CurrencyConfig, UnitConfig, ThemeConfig, NotificationConfig } from '../types';

// Storage keys
export const STORAGE_KEYS = {
  APP_CONFIG: 'app_config',
  FIRST_LAUNCH: 'first_launch',
  CURRENT_UNITS: 'current_units',
  THRESHOLD_LIMIT: 'threshold_limit',
  COST_PER_UNIT: 'cost_per_unit',
  SELECTED_CURRENCY: 'selected_currency',
  SELECTED_UNIT: 'selected_unit',
  SELECTED_THEME: 'selected_theme',
  SELECTED_FONT: 'selected_font',
  NOTIFICATION_SETTINGS: 'notification_settings',
  LAST_USAGE_DATE: 'last_usage_date',
  USER_PREFERENCES: 'user_preferences',
} as const;

// Default configurations
export const DEFAULT_CURRENCIES: CurrencyConfig[] = [
  { name: 'US Dollar', symbol: '$', code: 'USD', isCustom: false },
  { name: 'Euro', symbol: '€', code: 'EUR', isCustom: false },
  { name: 'British Pound', symbol: '£', code: 'GBP', isCustom: false },
  { name: 'South African Rand', symbol: 'R', code: 'ZAR', isCustom: false },
  { name: 'Japanese Yen', symbol: '¥', code: 'JPY', isCustom: false },
];

export const DEFAULT_UNITS: UnitConfig[] = [
  { name: 'Units', symbol: 'Units', isCustom: false },
  { name: 'Kilowatt Hours', symbol: 'kWh', isCustom: false },
];

export const DEFAULT_THEMES: ThemeConfig[] = [
  {
    id: 'electric_blue',
    name: 'Electric Blue',
    colors: {
      primary: '#007AFF',
      secondary: '#5AC8FA',
      background: '#F2F2F7',
      surface: '#FFFFFF',
      text: '#000000',
      textSecondary: '#6D6D80',
      accent: '#FF9500',
      warning: '#FF9500',
      error: '#FF3B30',
      success: '#34C759',
    },
    gradients: {
      primary: ['#007AFF', '#5AC8FA'],
      secondary: ['#5AC8FA', '#007AFF'],
      accent: ['#FF9500', '#FFCC02'],
    },
  },
  {
    id: 'lightning_yellow',
    name: 'Lightning Yellow',
    colors: {
      primary: '#FFCC02',
      secondary: '#FF9500',
      background: '#FFFBF0',
      surface: '#FFFFFF',
      text: '#1D1D1F',
      textSecondary: '#86868B',
      accent: '#007AFF',
      warning: '#FF9500',
      error: '#FF3B30',
      success: '#34C759',
    },
    gradients: {
      primary: ['#FFCC02', '#FF9500'],
      secondary: ['#FF9500', '#FFCC02'],
      accent: ['#007AFF', '#5AC8FA'],
    },
  },
  {
    id: 'power_green',
    name: 'Power Green',
    colors: {
      primary: '#34C759',
      secondary: '#30D158',
      background: '#F0FFF4',
      surface: '#FFFFFF',
      text: '#1D1D1F',
      textSecondary: '#86868B',
      accent: '#007AFF',
      warning: '#FF9500',
      error: '#FF3B30',
      success: '#34C759',
    },
    gradients: {
      primary: ['#34C759', '#30D158'],
      secondary: ['#30D158', '#34C759'],
      accent: ['#007AFF', '#5AC8FA'],
    },
  },
  {
    id: 'dark_mode',
    name: 'Dark Mode',
    colors: {
      primary: '#0A84FF',
      secondary: '#64D2FF',
      background: '#000000',
      surface: '#1C1C1E',
      text: '#FFFFFF',
      textSecondary: '#8E8E93',
      accent: '#FF9F0A',
      warning: '#FF9F0A',
      error: '#FF453A',
      success: '#32D74B',
    },
    gradients: {
      primary: ['#0A84FF', '#64D2FF'],
      secondary: ['#64D2FF', '#0A84FF'],
      accent: ['#FF9F0A', '#FFCC02'],
    },
  },
  {
    id: 'sunset_orange',
    name: 'Sunset Orange',
    colors: {
      primary: '#FF6B35',
      secondary: '#FF8E53',
      background: '#FFF8F0',
      surface: '#FFFFFF',
      text: '#1D1D1F',
      textSecondary: '#86868B',
      accent: '#007AFF',
      warning: '#FF9500',
      error: '#FF3B30',
      success: '#34C759',
    },
    gradients: {
      primary: ['#FF6B35', '#FF8E53'],
      secondary: ['#FF8E53', '#FF6B35'],
      accent: ['#007AFF', '#5AC8FA'],
    },
  },
];

export const DEFAULT_FONTS = [
  'System',
  'Roboto',
  'Open Sans',
  'Lato',
  'Montserrat',
];

export const DEFAULT_NOTIFICATION_CONFIG: NotificationConfig = {
  enabled: true,
  time: '18:00',
  title: 'Electricity Usage Reminder',
  message: 'Don\'t forget to record your electricity usage today!',
};

export const DEFAULT_APP_CONFIG: AppConfig = {
  isFirstLaunch: true,
  currentUnits: 0,
  thresholdLimit: 10,
  costPerUnit: 0.15,
  selectedCurrency: DEFAULT_CURRENCIES[0],
  selectedUnit: DEFAULT_UNITS[0],
  selectedTheme: 'electric_blue',
  selectedFont: 'System',
  notificationSettings: DEFAULT_NOTIFICATION_CONFIG,
  lastUsageDate: new Date().toISOString(),
};

// Storage utility functions
export const storeData = async (key: string, value: any): Promise<void> => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (error) {
    console.error(`Error storing data for key ${key}:`, error);
    throw error;
  }
};

export const getData = async <T>(key: string, defaultValue?: T): Promise<T | null> => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    if (jsonValue != null) {
      return JSON.parse(jsonValue) as T;
    }
    return defaultValue || null;
  } catch (error) {
    console.error(`Error getting data for key ${key}:`, error);
    return defaultValue || null;
  }
};

export const removeData = async (key: string): Promise<void> => {
  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing data for key ${key}:`, error);
    throw error;
  }
};

export const clearAllData = async (): Promise<void> => {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error('Error clearing all data:', error);
    throw error;
  }
};

// App-specific storage functions
export const getAppConfig = async (): Promise<AppConfig> => {
  const config = await getData<AppConfig>(STORAGE_KEYS.APP_CONFIG);
  return config || DEFAULT_APP_CONFIG;
};

export const setAppConfig = async (config: AppConfig): Promise<void> => {
  await storeData(STORAGE_KEYS.APP_CONFIG, config);
};

export const updateAppConfig = async (updates: Partial<AppConfig>): Promise<AppConfig> => {
  const currentConfig = await getAppConfig();
  const updatedConfig = { ...currentConfig, ...updates };
  await setAppConfig(updatedConfig);
  return updatedConfig;
};

export const isFirstLaunch = async (): Promise<boolean> => {
  const config = await getAppConfig();
  return config.isFirstLaunch;
};

export const setFirstLaunchComplete = async (): Promise<void> => {
  await updateAppConfig({ isFirstLaunch: false });
};

export const getCurrentUnits = async (): Promise<number> => {
  const config = await getAppConfig();
  return config.currentUnits;
};

export const setCurrentUnits = async (units: number): Promise<void> => {
  await updateAppConfig({ currentUnits: units });
};

export const getThresholdLimit = async (): Promise<number> => {
  const config = await getAppConfig();
  return config.thresholdLimit;
};

export const setThresholdLimit = async (limit: number): Promise<void> => {
  await updateAppConfig({ thresholdLimit: limit });
};

export const getCostPerUnit = async (): Promise<number> => {
  const config = await getAppConfig();
  return config.costPerUnit;
};

export const setCostPerUnit = async (cost: number): Promise<void> => {
  await updateAppConfig({ costPerUnit: cost });
};

export const getSelectedCurrency = async (): Promise<CurrencyConfig> => {
  const config = await getAppConfig();
  return config.selectedCurrency;
};

export const setSelectedCurrency = async (currency: CurrencyConfig): Promise<void> => {
  await updateAppConfig({ selectedCurrency: currency });
};

export const getSelectedUnit = async (): Promise<UnitConfig> => {
  const config = await getAppConfig();
  return config.selectedUnit;
};

export const setSelectedUnit = async (unit: UnitConfig): Promise<void> => {
  await updateAppConfig({ selectedUnit: unit });
};

export const getSelectedTheme = async (): Promise<string> => {
  const config = await getAppConfig();
  return config.selectedTheme;
};

export const setSelectedTheme = async (themeId: string): Promise<void> => {
  await updateAppConfig({ selectedTheme: themeId });
};

export const getNotificationSettings = async (): Promise<NotificationConfig> => {
  const config = await getAppConfig();
  return config.notificationSettings;
};

export const setNotificationSettings = async (settings: NotificationConfig): Promise<void> => {
  await updateAppConfig({ notificationSettings: settings });
};
