{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../src/hooks.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,KAAK,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAEtF,OAAO,UAAU,MAAM,cAAc,CAAC;AAEtC,OAAO,EAAE,iBAAiB,EAAuB,MAAM,kBAAkB,CAAC;AAC1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AA6EjD;;GAEG;AACH,MAAM,aAAa,GAAG,aAAa,CAAwB,IAAI,CAAC,CAAC;AAEjE;;;GAGG;AACH,MAAM,UAAU,cAAc,CAAC,EAC7B,QAAQ,EACR,OAAO,EACP,WAAW,GAAG,KAAK,EACnB,GAAG,KAAK,EACY;IACpB,IAAI,OAAO,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;QACnC,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;IAC5F,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,CAAC,sBAAsB,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,sBAAsB,CAAC,CAAC;IAChF,CAAC;IAED,OAAO,CACL,CAAC,yBAAyB,CAAC,IAAI,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CACrD;MAAA,CAAC,QAAQ,CACX;IAAA,EAAE,yBAAyB,CAAC,CAC7B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,UAAU,gBAAgB;IAC9B,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;IAC1C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAWD,IAAI,gBAAgB,GAAgC,IAAI,CAAC;AAEzD,SAAS,sBAAsB,CAAC,EAC9B,YAAY,EACZ,SAAS,EACT,OAAO,EACP,WAAW,EACX,QAAQ,EACR,MAAM,GAC+C;IACrD,MAAM,eAAe,GAAG,gBAAgB,CAAC;QACvC,YAAY;QACZ,SAAS;QACT,OAAO;QACP,WAAW;QACX,MAAM;KACP,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,GAAG,CAAC,eAAe,CAAC,CAAC;IACtC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;AACtF,CAAC;AAED,SAAS,yBAAyB,CAAC,EACjC,YAAY,EACZ,SAAS,EACT,OAAO,EACP,WAAW,EACX,QAAQ,EACR,MAAM,EACN,OAAO,GACkC;IACzC,MAAM,WAAW,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAe,IAAI,CAAC,CAAC;IAEvD,SAAS,CAAC,GAAG,EAAE;QACb,KAAK,UAAU,KAAK;YAClB,IAAI,CAAC;gBACH,MAAM,EAAE,GAAG,MAAM,yBAAyB,CAAC;oBACzC,YAAY;oBACZ,SAAS;oBACT,OAAO;oBACP,WAAW;oBACX,MAAM;iBACP,CAAC,CAAC;gBACH,WAAW,CAAC,OAAO,GAAG,EAAE,CAAC;gBACzB,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,QAAQ,CAAC,CAAC,CAAC,CAAC;YACd,CAAC;QACH,CAAC;QAED,KAAK,UAAU,QAAQ,CAAC,EAAyB;YAC/C,IAAI,CAAC;gBACH,MAAM,EAAE,EAAE,UAAU,EAAE,CAAC;YACzB,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,QAAQ,CAAC,CAAC,CAAC,CAAC;YACd,CAAC;QACH,CAAC;QAED,KAAK,EAAE,CAAC;QAER,OAAO,GAAG,EAAE;YACV,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC;YAC/B,QAAQ,CAAC,EAAE,CAAC,CAAC;YACb,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;YAC3B,UAAU,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;IAE/C,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,MAAM,OAAO,GACX,OAAO;YACP,CAAC,CAAC,CAAC,EAAE,EAAE;gBACL,MAAM,CAAC,CAAC;YACV,CAAC,CAAC,CAAC;QACL,OAAO,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IACD,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;AACjG,CAAC;AAED,SAAS,gBAAgB,CAAC,EACxB,YAAY,EACZ,SAAS,EACT,OAAO,EACP,WAAW,EACX,MAAM,GAIP;IACC,IACE,gBAAgB,EAAE,OAAO,IAAI,IAAI;QACjC,gBAAgB,EAAE,YAAY,KAAK,YAAY;QAC/C,gBAAgB,EAAE,SAAS,KAAK,SAAS;QACzC,gBAAgB,EAAE,OAAO,KAAK,OAAO;QACrC,gBAAgB,EAAE,MAAM,KAAK,MAAM,EACnC,CAAC;QACD,OAAO,gBAAgB,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,IAAI,OAAgC,CAAC;IACrC,IAAI,gBAAgB,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC;QACtC,OAAO,GAAG,gBAAgB,CAAC,OAAO;aAC/B,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE;YACX,EAAE,CAAC,UAAU,EAAE,CAAC;QAClB,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,yBAAyB,CAAC;gBAC/B,YAAY;gBACZ,SAAS;gBACT,OAAO;gBACP,WAAW;gBACX,MAAM;aACP,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,yBAAyB,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;IACjG,CAAC;IACD,gBAAgB,GAAG;QACjB,YAAY;QACZ,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;KACR,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,KAAK,UAAU,yBAAyB,CAAC,EACvC,YAAY,EACZ,SAAS,EACT,OAAO,EACP,WAAW,EACX,MAAM,GAIP;IACC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;QACxB,MAAM,4BAA4B,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;IAC3E,CAAC;IACD,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAC3E,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;IACzB,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,4BAA4B,CAChD,YAAoB,EACpB,WAAsC,EACtC,SAAkB;IAElB,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC;IAC1E,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,yCAAyC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;IAClF,CAAC;IACD,MAAM,IAAI,GAAG,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACzD,MAAM,UAAU,CAAC,wBAAwB,CACvC,IAAI,EACJ,KAAK,CAAC,QAAQ,EACd,WAAW,CAAC,cAAc,IAAI,KAAK,CACpC,CAAC;AACJ,CAAC;AAaD,6IAA6I;AAC7I;;GAEG;AACH,SAAS,GAAG,CAAI,OAAwC;IACtD,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;QAC/B,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YACnC,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACnE,CAAC;YACD,OAAO,OAAO,CAAC,KAAK,CAAC;QACvB,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACzC,MAAM,OAAO,CAAC,MAAM,CAAC;QACvB,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACxC,MAAM,OAAO,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAC1D,CAAC;IAED,MAAM,eAAe,GAAG,OAA6B,CAAC;IACtD,eAAe,CAAC,MAAM,GAAG,SAAS,CAAC;IACnC,eAAe,CAAC,IAAI,CAClB,CAAC,MAAS,EAAE,EAAE;QACZ,eAAe,CAAC,MAAM,GAAG,WAAW,CAAC;QACrC,eAAe,CAAC,KAAK,GAAG,MAAM,CAAC;IACjC,CAAC,EACD,CAAC,MAAM,EAAE,EAAE;QACT,eAAe,CAAC,MAAM,GAAG,UAAU,CAAC;QACpC,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;IAClC,CAAC,CACF,CAAC;IACF,MAAM,eAAe,CAAC;AACxB,CAAC;AAED,SAAS,iBAAiB,CACxB,OAAwC;IAExC,OAAO,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,IAAI,QAAQ,IAAI,OAAO,CAAC;AAChF,CAAC;AAED,YAAY", "sourcesContent": ["import { Asset } from 'expo-asset';\nimport React, { createContext, useContext, useEffect, useRef, useState } from 'react';\n\nimport ExpoSQLite from './ExpoSQLite';\nimport type { SQLiteOpenOptions } from './NativeDatabase';\nimport { openDatabaseAsync, type SQLiteDatabase } from './SQLiteDatabase';\nimport { createDatabasePath } from './pathUtils';\n\nexport interface SQLiteProviderAssetSource {\n  /**\n   * The asset ID returned from the `require()` call.\n   */\n  assetId: number;\n\n  /**\n   * Force overwrite the local database file even if it already exists.\n   * @default false\n   */\n  forceOverwrite?: boolean;\n}\n\nexport interface SQLiteProviderProps {\n  /**\n   * The name of the database file to open.\n   */\n  databaseName: string;\n\n  /**\n   * The directory where the database file is located.\n   * @default defaultDatabaseDirectory\n   */\n  directory?: string;\n\n  /**\n   * Open options.\n   */\n  options?: SQLiteOpenOptions;\n\n  /**\n   * Import a bundled database file from the specified asset module.\n   * @example\n   * ```ts\n   * assetSource={{ assetId: require('./assets/db.db') }}\n   * ```\n   */\n  assetSource?: SQLiteProviderAssetSource;\n\n  /**\n   * The children to render.\n   */\n  children: React.ReactNode;\n\n  /**\n   * A custom initialization handler to run before rendering the children.\n   * You can use this to run database migrations or other setup tasks.\n   */\n  onInit?: (db: SQLiteDatabase) => Promise<void>;\n\n  /**\n   * Handle errors from SQLiteProvider.\n   * @default rethrow the error\n   */\n  onError?: (error: Error) => void;\n\n  /**\n   * Enable [`React.Suspense`](https://react.dev/reference/react/Suspense) integration.\n   * @default false\n   * @example\n   * ```tsx\n   * export default function App() {\n   *   return (\n   *     <Suspense fallback={<Text>Loading...</Text>}>\n   *       <SQLiteProvider databaseName=\"test.db\" useSuspense={true}>\n   *         <Main />\n   *       </SQLiteProvider>\n   *     </Suspense>\n   *   );\n   * }\n   * ```\n   */\n  useSuspense?: boolean;\n}\n\n/**\n * Create a context for the SQLite database\n */\nconst SQLiteContext = createContext<SQLiteDatabase | null>(null);\n\n/**\n * Context.Provider component that provides a SQLite database to all children.\n * All descendants of this component will be able to access the database using the [`useSQLiteContext`](#usesqlitecontext) hook.\n */\nexport function SQLiteProvider({\n  children,\n  onError,\n  useSuspense = false,\n  ...props\n}: SQLiteProviderProps) {\n  if (onError != null && useSuspense) {\n    throw new Error('Cannot use `onError` with `useSuspense`, use error boundaries instead.');\n  }\n\n  if (useSuspense) {\n    return <SQLiteProviderSuspense {...props}>{children}</SQLiteProviderSuspense>;\n  }\n\n  return (\n    <SQLiteProviderNonSuspense {...props} onError={onError}>\n      {children}\n    </SQLiteProviderNonSuspense>\n  );\n}\n\n/**\n * A global hook for accessing the SQLite database across components.\n * This hook should only be used within a [`<SQLiteProvider>`](#sqliteprovider) component.\n *\n * @example\n * ```tsx\n * export default function App() {\n *   return (\n *     <SQLiteProvider databaseName=\"test.db\">\n *       <Main />\n *     </SQLiteProvider>\n *   );\n * }\n *\n * export function Main() {\n *   const db = useSQLiteContext();\n *   console.log('sqlite version', db.getFirstSync('SELECT sqlite_version()'));\n *   return <View />\n * }\n * ```\n */\nexport function useSQLiteContext(): SQLiteDatabase {\n  const context = useContext(SQLiteContext);\n  if (context == null) {\n    throw new Error('useSQLiteContext must be used within a <SQLiteProvider>');\n  }\n  return context;\n}\n\n//#region Internals\n\ntype DatabaseInstanceType = Pick<\n  SQLiteProviderProps,\n  'databaseName' | 'directory' | 'options' | 'onInit'\n> & {\n  promise: Promise<SQLiteDatabase> | null;\n};\n\nlet databaseInstance: DatabaseInstanceType | null = null;\n\nfunction SQLiteProviderSuspense({\n  databaseName,\n  directory,\n  options,\n  assetSource,\n  children,\n  onInit,\n}: Omit<SQLiteProviderProps, 'onError' | 'useSuspense'>) {\n  const databasePromise = getDatabaseAsync({\n    databaseName,\n    directory,\n    options,\n    assetSource,\n    onInit,\n  });\n  const database = use(databasePromise);\n  return <SQLiteContext.Provider value={database}>{children}</SQLiteContext.Provider>;\n}\n\nfunction SQLiteProviderNonSuspense({\n  databaseName,\n  directory,\n  options,\n  assetSource,\n  children,\n  onInit,\n  onError,\n}: Omit<SQLiteProviderProps, 'useSuspense'>) {\n  const databaseRef = useRef<SQLiteDatabase | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n\n  useEffect(() => {\n    async function setup() {\n      try {\n        const db = await openDatabaseWithInitAsync({\n          databaseName,\n          directory,\n          options,\n          assetSource,\n          onInit,\n        });\n        databaseRef.current = db;\n        setLoading(false);\n      } catch (e: any) {\n        setError(e);\n      }\n    }\n\n    async function teardown(db: SQLiteDatabase | null) {\n      try {\n        await db?.closeAsync();\n      } catch (e: any) {\n        setError(e);\n      }\n    }\n\n    setup();\n\n    return () => {\n      const db = databaseRef.current;\n      teardown(db);\n      databaseRef.current = null;\n      setLoading(true);\n    };\n  }, [databaseName, directory, options, onInit]);\n\n  if (error != null) {\n    const handler =\n      onError ??\n      ((e) => {\n        throw e;\n      });\n    handler(error);\n  }\n  if (loading || !databaseRef.current) {\n    return null;\n  }\n  return <SQLiteContext.Provider value={databaseRef.current}>{children}</SQLiteContext.Provider>;\n}\n\nfunction getDatabaseAsync({\n  databaseName,\n  directory,\n  options,\n  assetSource,\n  onInit,\n}: Pick<\n  SQLiteProviderProps,\n  'databaseName' | 'directory' | 'options' | 'assetSource' | 'onInit'\n>): Promise<SQLiteDatabase> {\n  if (\n    databaseInstance?.promise != null &&\n    databaseInstance?.databaseName === databaseName &&\n    databaseInstance?.directory === directory &&\n    databaseInstance?.options === options &&\n    databaseInstance?.onInit === onInit\n  ) {\n    return databaseInstance.promise;\n  }\n\n  let promise: Promise<SQLiteDatabase>;\n  if (databaseInstance?.promise != null) {\n    promise = databaseInstance.promise\n      .then((db) => {\n        db.closeAsync();\n      })\n      .then(() => {\n        return openDatabaseWithInitAsync({\n          databaseName,\n          directory,\n          options,\n          assetSource,\n          onInit,\n        });\n      });\n  } else {\n    promise = openDatabaseWithInitAsync({ databaseName, directory, options, assetSource, onInit });\n  }\n  databaseInstance = {\n    databaseName,\n    directory,\n    options,\n    onInit,\n    promise,\n  };\n  return promise;\n}\n\nasync function openDatabaseWithInitAsync({\n  databaseName,\n  directory,\n  options,\n  assetSource,\n  onInit,\n}: Pick<\n  SQLiteProviderProps,\n  'databaseName' | 'directory' | 'options' | 'assetSource' | 'onInit'\n>): Promise<SQLiteDatabase> {\n  if (assetSource != null) {\n    await importDatabaseFromAssetAsync(databaseName, assetSource, directory);\n  }\n  const database = await openDatabaseAsync(databaseName, options, directory);\n  if (onInit != null) {\n    await onInit(database);\n  }\n  return database;\n}\n\n/**\n * Imports an asset database into the SQLite database directory.\n *\n * Exposed only for testing purposes.\n * @hidden\n */\nexport async function importDatabaseFromAssetAsync(\n  databaseName: string,\n  assetSource: SQLiteProviderAssetSource,\n  directory?: string\n) {\n  const asset = await Asset.fromModule(assetSource.assetId).downloadAsync();\n  if (!asset.localUri) {\n    throw new Error(`Unable to get the localUri from asset ${assetSource.assetId}`);\n  }\n  const path = createDatabasePath(databaseName, directory);\n  await ExpoSQLite.importAssetDatabaseAsync(\n    path,\n    asset.localUri,\n    assetSource.forceOverwrite ?? false\n  );\n}\n\n//#endregion\n\n//#region Private Suspense API similar to `React.use`\n\n// Referenced from https://github.com/vercel/swr/blob/1d8110900d1aee3747199bfb377b149b7ff6848e/_internal/src/types.ts#L27-L31\ntype ReactUsePromise<T, E extends Error = Error> = Promise<T> & {\n  status?: 'pending' | 'fulfilled' | 'rejected';\n  value?: T;\n  reason?: E;\n};\n\n// Referenced from https://github.com/reactjs/react.dev/blob/6570e6cd79a16ac3b1a2902632eddab7e6abb9ad/src/content/reference/react/Suspense.md\n/**\n * A custom hook like [`React.use`](https://react.dev/reference/react/use) hook using private Suspense implementation.\n */\nfunction use<T>(promise: Promise<T> | ReactUsePromise<T>) {\n  if (isReactUsePromise(promise)) {\n    if (promise.status === 'fulfilled') {\n      if (promise.value === undefined) {\n        throw new Error('[use] Unexpected undefined value from promise');\n      }\n      return promise.value;\n    } else if (promise.status === 'rejected') {\n      throw promise.reason;\n    } else if (promise.status === 'pending') {\n      throw promise;\n    }\n    throw new Error('[use] Promise is in an invalid state');\n  }\n\n  const suspensePromise = promise as ReactUsePromise<T>;\n  suspensePromise.status = 'pending';\n  suspensePromise.then(\n    (result: T) => {\n      suspensePromise.status = 'fulfilled';\n      suspensePromise.value = result;\n    },\n    (reason) => {\n      suspensePromise.status = 'rejected';\n      suspensePromise.reason = reason;\n    }\n  );\n  throw suspensePromise;\n}\n\nfunction isReactUsePromise<T>(\n  promise: Promise<T> | ReactUsePromise<T>\n): promise is ReactUsePromise<T> {\n  return typeof promise === 'object' && promise !== null && 'status' in promise;\n}\n\n//#endregion\n"]}