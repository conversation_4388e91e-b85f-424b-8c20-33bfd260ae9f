import type { <PERSON>vas<PERSON><PERSON>, Paint } from "canvaskit-wasm";
import type { <PERSON>roke<PERSON>oin, BlendMode, SkColor, SkColorFilter, SkImageFilter, SkPaint, SkShader, StrokeCap, PaintStyle, SkMaskFilter, SkPathEffect } from "../types";
import { HostObject } from "./Host";
export declare class JsiSkPaint extends HostObject<Paint, "Paint"> implements SkPaint {
    constructor(CanvasKit: CanvasKit, ref: Paint);
    dispose: () => void;
    copy(): JsiSkPaint;
    assign(paint: JsiSkPaint): void;
    reset(): void;
    getAlphaf(): number;
    getColor(): import("canvaskit-wasm").Color;
    getStrokeCap(): number;
    getStrokeJoin(): number;
    getStrokeMiter(): number;
    getStrokeWidth(): number;
    setAlphaf(alpha: number): void;
    setAntiAlias(aa: boolean): void;
    setDither(dither: boolean): void;
    setBlendMode(blendMode: BlendMode): void;
    setColor(color: SkColor): void;
    setColorFilter(filter: SkColorFilter | null): void;
    setImageFilter(filter: SkImageFilter | null): void;
    setMaskFilter(filter: SkMaskFilter | null): void;
    setPathEffect(effect: SkPathEffect | null): void;
    setShader(shader: SkShader | null): void;
    setStrokeCap(cap: StrokeCap): void;
    setStrokeJoin(join: StrokeJoin): void;
    setStrokeMiter(limit: number): void;
    setStrokeWidth(width: number): void;
    setStyle(style: PaintStyle): void;
}
