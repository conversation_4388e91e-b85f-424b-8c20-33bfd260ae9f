{"version": 3, "file": "NativeStatement.js", "sourceRoot": "", "sources": ["../src/NativeStatement.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * A result returned by [`SQLiteDatabase.runAsync`](#runasyncsource-params) or [`SQLiteDatabase.runSync`](#runsyncsource-params).\n */\nexport interface SQLiteRunResult {\n  /**\n   * The last inserted row ID. Returned from the [`sqlite3_last_insert_rowid()`](https://www.sqlite.org/c3ref/last_insert_rowid.html) function.\n   */\n  lastInsertRowId: number;\n\n  /**\n   * The number of rows affected. Returned from the [`sqlite3_changes()`](https://www.sqlite.org/c3ref/changes.html) function.\n   */\n  changes: number;\n}\n\n/**\n * Bind parameters to the prepared statement.\n * You can either pass the parameters in the following forms:\n *\n * @example\n * A single array for unnamed parameters.\n * ```ts\n * const statement = await db.prepareAsync('SELECT * FROM test WHERE value = ? AND intValue = ?');\n * const result = await statement.executeAsync(['test1', 789]);\n * const firstRow = await result.getFirstAsync();\n * ```\n *\n * @example\n * Variadic arguments for unnamed parameters.\n * ```ts\n * const statement = await db.prepareAsync('SELECT * FROM test WHERE value = ? AND intValue = ?');\n * const result = await statement.executeAsync('test1', 789);\n * const firstRow = await result.getFirstAsync();\n * ```\n *\n * @example\n * A single object for [named parameters](https://www.sqlite.org/lang_expr.html)\n *\n * We support multiple named parameter forms such as `:VVV`, `@VVV`, and `$VVV`. We recommend using `$VVV` because JavaScript allows using `$` in identifiers without escaping.\n * ```ts\n * const statement = await db.prepareAsync('SELECT * FROM test WHERE value = $value AND intValue = $intValue');\n * const result = await statement.executeAsync({ $value: 'test1', $intValue: 789 });\n * const firstRow = await result.getFirstAsync();\n * ```\n */\nexport type SQLiteBindValue = string | number | null | boolean | Uint8Array;\nexport type SQLiteBindParams = Record<string, SQLiteBindValue> | SQLiteBindValue[];\nexport type SQLiteVariadicBindParams = SQLiteBindValue[];\n\nexport type SQLiteBindPrimitiveParams = Record<string, Exclude<SQLiteBindValue, Uint8Array>>;\nexport type SQLiteBindBlobParams = Record<string, Uint8Array>;\nexport type SQLiteColumnNames = string[];\nexport type SQLiteColumnValues = any[];\nexport type SQLiteAnyDatabase = any;\n\n/**\n * A class that represents an instance of the SQLite statement.\n */\nexport declare class NativeStatement {\n  //#region Asynchronous API\n\n  public runAsync(\n    database: SQLiteAnyDatabase,\n    bindParams: SQLiteBindPrimitiveParams,\n    bindBlobParams: SQLiteBindBlobParams,\n    shouldPassAsArray: boolean\n  ): Promise<SQLiteRunResult & { firstRowValues: SQLiteColumnValues }>;\n  public stepAsync(database: SQLiteAnyDatabase): Promise<SQLiteColumnValues | null | undefined>;\n  public getAllAsync(database: SQLiteAnyDatabase): Promise<SQLiteColumnValues[]>;\n  public resetAsync(database: SQLiteAnyDatabase): Promise<void>;\n  public getColumnNamesAsync(): Promise<SQLiteColumnNames>;\n  public finalizeAsync(database: SQLiteAnyDatabase): Promise<void>;\n\n  //#endregion\n\n  //#region Synchronous API\n\n  public runSync(\n    database: SQLiteAnyDatabase,\n    bindParams: SQLiteBindPrimitiveParams,\n    bindBlobParams: SQLiteBindBlobParams,\n    shouldPassAsArray: boolean\n  ): SQLiteRunResult & { firstRowValues: SQLiteColumnValues };\n  public stepSync(database: SQLiteAnyDatabase): SQLiteColumnValues | null | undefined;\n  public getAllSync(database: SQLiteAnyDatabase): SQLiteColumnValues[];\n  public resetSync(database: SQLiteAnyDatabase): void;\n  public getColumnNamesSync(): string[];\n  public finalizeSync(database: SQLiteAnyDatabase): void;\n\n  //#endregion\n}\n"]}