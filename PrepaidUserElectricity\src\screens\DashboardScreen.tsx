import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

// We'll implement these components later
import ModernDial from '../components/ModernDial';
import QuickActionButton from '../components/QuickActionButton';
import StatsCard from '../components/StatsCard';

const DashboardScreen = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState({
    currentUnits: 85.5,
    thresholdLimit: 20,
    usageSinceLastRecording: 12.3,
    weeklyTotal: { usage: 45.2, currency: 67.80 },
    monthlyTotal: { usage: 180.5, currency: 271.25 },
    lastRecordingDate: '2024-01-15',
    selectedUnit: 'kWh',
    selectedCurrency: 'USD',
    costPerUnit: 0.15,
  });

  const isLowUnits = dashboardData.currentUnits <= dashboardData.thresholdLimit;

  useEffect(() => {
    // Show low units warning if threshold is reached
    if (isLowUnits) {
      Alert.alert(
        'Low Units Warning',
        `Your current units (${dashboardData.currentUnits} ${dashboardData.selectedUnit}) are below the threshold limit of ${dashboardData.thresholdLimit} ${dashboardData.selectedUnit}.`,
        [{ text: 'OK' }]
      );
    }
  }, [isLowUnits]);

  const onRefresh = async () => {
    setRefreshing(true);
    // TODO: Implement data refresh from database
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const quickActions = [
    {
      title: 'Add Purchase',
      icon: 'add-shopping-cart',
      color: '#34C759',
      onPress: () => navigation.navigate('Purchases' as never),
    },
    {
      title: 'Record Usage',
      icon: 'trending-up',
      color: '#007AFF',
      onPress: () => navigation.navigate('Usage' as never),
    },
    {
      title: 'View History',
      icon: 'history',
      color: '#FF9500',
      onPress: () => navigation.navigate('History' as never),
    },
  ];

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Current Units Display with Modern Dial */}
      <View style={styles.dialContainer}>
        <Text style={styles.sectionTitle}>Current Units</Text>
        <ModernDial
          value={dashboardData.currentUnits}
          maxValue={200}
          size={200}
          strokeWidth={20}
          color={isLowUnits ? '#FF3B30' : '#007AFF'}
          backgroundColor="#E5E5EA"
          label="Units Remaining"
          unit={dashboardData.selectedUnit}
        />
        
        {isLowUnits && (
          <View style={styles.warningContainer}>
            <Icon name="warning" size={20} color="#FF3B30" />
            <Text style={styles.warningText}>Low Units Warning!</Text>
          </View>
        )}
      </View>

      {/* Usage Since Last Recording */}
      <View style={styles.usageContainer}>
        <StatsCard
          title="Usage Since Last Recording"
          value={dashboardData.usageSinceLastRecording}
          unit={dashboardData.selectedUnit}
          icon="trending-up"
          color="#FF9500"
          subtitle={`Last recorded: ${dashboardData.lastRecordingDate}`}
        />
      </View>

      {/* Weekly and Monthly Totals */}
      <View style={styles.totalsContainer}>
        <View style={styles.totalRow}>
          <StatsCard
            title="Weekly Total"
            value={dashboardData.weeklyTotal.usage}
            unit={dashboardData.selectedUnit}
            icon="date-range"
            color="#34C759"
            subtitle={`$${dashboardData.weeklyTotal.currency.toFixed(2)}`}
            style={styles.halfCard}
          />
          <StatsCard
            title="Monthly Total"
            value={dashboardData.monthlyTotal.usage}
            unit={dashboardData.selectedUnit}
            icon="calendar-today"
            color="#5AC8FA"
            subtitle={`$${dashboardData.monthlyTotal.currency.toFixed(2)}`}
            style={styles.halfCard}
          />
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActionsGrid}>
          {quickActions.map((action, index) => (
            <QuickActionButton
              key={index}
              title={action.title}
              icon={action.icon}
              color={action.color}
              onPress={action.onPress}
            />
          ))}
        </View>
      </View>

      {/* Cost Information */}
      <View style={styles.costContainer}>
        <StatsCard
          title="Cost Per Unit"
          value={dashboardData.costPerUnit}
          unit={`${dashboardData.selectedCurrency}/${dashboardData.selectedUnit}`}
          icon="attach-money"
          color="#FF6B35"
          subtitle="Current rate"
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  dialContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 15,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFE5E5',
    padding: 10,
    borderRadius: 8,
    marginTop: 15,
  },
  warningText: {
    color: '#FF3B30',
    fontWeight: '600',
    marginLeft: 8,
  },
  usageContainer: {
    margin: 15,
    marginTop: 0,
  },
  totalsContainer: {
    margin: 15,
    marginTop: 0,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfCard: {
    flex: 0.48,
  },
  quickActionsContainer: {
    margin: 15,
    marginTop: 0,
  },
  quickActionsGrid: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  costContainer: {
    margin: 15,
    marginTop: 0,
    marginBottom: 30,
  },
});

export default DashboardScreen;
