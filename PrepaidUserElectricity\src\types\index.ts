// Core data types for the Prepaid Electricity App

export interface Purchase {
  id?: number;
  currency_amount: number;
  unit_amount: number;
  currency_type: string;
  unit_type: string;
  cost_per_unit: number;
  purchase_date: string;
  notes?: string;
}

export interface UsageRecord {
  id?: number;
  previous_units: number;
  current_units: number;
  usage_amount: number;
  unit_type: string;
  record_date: string;
  notes?: string;
}

export interface HistoryEntry {
  id?: number;
  type: 'purchase' | 'usage';
  reference_id: number;
  amount: number;
  currency_value?: number;
  unit_type: string;
  currency_type?: string;
  date: string;
  description?: string;
}

export interface TotalRecord {
  id?: number;
  period_type: 'daily' | 'weekly' | 'monthly';
  period_start: string;
  period_end: string;
  total_purchases_currency: number;
  total_purchases_units: number;
  total_usage_units: number;
  currency_type: string;
  unit_type: string;
  created_at: string;
  updated_at: string;
}

export interface AppSettings {
  id?: number;
  key: string;
  value: string;
  created_at: string;
  updated_at: string;
}

// App configuration types
export interface CurrencyConfig {
  name: string;
  symbol: string;
  code: string;
  isCustom: boolean;
}

export interface UnitConfig {
  name: string;
  symbol: string;
  isCustom: boolean;
}

export interface ThemeConfig {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    accent: string;
    warning: string;
    error: string;
    success: string;
  };
  gradients: {
    primary: string[];
    secondary: string[];
    accent: string[];
  };
}

export interface NotificationConfig {
  enabled: boolean;
  time: string; // HH:MM format
  title: string;
  message: string;
}

export interface AppConfig {
  isFirstLaunch: boolean;
  currentUnits: number;
  thresholdLimit: number;
  costPerUnit: number;
  selectedCurrency: CurrencyConfig;
  selectedUnit: UnitConfig;
  selectedTheme: string;
  selectedFont: string;
  notificationSettings: NotificationConfig;
  lastUsageDate: string;
}

// Navigation types
export type RootStackParamList = {
  Main: undefined;
  Dashboard: undefined;
  Purchases: undefined;
  Usage: undefined;
  History: undefined;
  Settings: undefined;
  GeneralSettings: undefined;
  AppearanceSettings: undefined;
  ResetOptions: undefined;
  InitialSetup: undefined;
};

// Redux state types
export interface DashboardState {
  currentUnits: number;
  usageSinceLastRecording: number;
  weeklyTotal: {
    usage: number;
    currency: number;
  };
  monthlyTotal: {
    usage: number;
    currency: number;
  };
  isLowUnitsWarning: boolean;
  lastRecordingDate: string;
}

export interface PurchasesState {
  purchases: Purchase[];
  isLoading: boolean;
  error: string | null;
  livePreview: {
    currencyAmount: number;
    unitAmount: number;
    costPerUnit: number;
  };
}

export interface UsageState {
  usageRecords: UsageRecord[];
  isLoading: boolean;
  error: string | null;
  currentInput: {
    previousUnits: number;
    currentUnits: number;
    calculatedUsage: number;
  };
}

export interface HistoryState {
  entries: HistoryEntry[];
  totals: TotalRecord[];
  isLoading: boolean;
  error: string | null;
  filter: {
    period: 'week' | 'month' | 'all';
    type: 'all' | 'purchase' | 'usage';
  };
}

export interface SettingsState {
  config: AppConfig;
  availableCurrencies: CurrencyConfig[];
  availableUnits: UnitConfig[];
  availableThemes: ThemeConfig[];
  availableFonts: string[];
  isLoading: boolean;
  error: string | null;
}

export interface RootState {
  dashboard: DashboardState;
  purchases: PurchasesState;
  usage: UsageState;
  history: HistoryState;
  settings: SettingsState;
}

// Utility calculation function type
export interface UsageCalculation {
  previousUnits: number;
  currentUnits: number;
  usageSinceLastRecording: number;
}

// Chart data types
export interface ChartDataPoint {
  x: string | number;
  y: number;
  label?: string;
}

export interface ChartData {
  data: ChartDataPoint[];
  title: string;
  color: string;
  gradient?: string[];
}

// Component prop types
export interface ModernDialProps {
  value: number;
  maxValue: number;
  size: number;
  strokeWidth: number;
  color: string;
  backgroundColor: string;
  gradient?: string[];
  label: string;
  unit: string;
}

export interface QuickActionButtonProps {
  title: string;
  icon: string;
  onPress: () => void;
  color: string;
  gradient?: string[];
}

export interface SettingsItemProps {
  title: string;
  subtitle?: string;
  icon: string;
  onPress: () => void;
  rightElement?: React.ReactNode;
  showArrow?: boolean;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: AppError;
  message?: string;
}
