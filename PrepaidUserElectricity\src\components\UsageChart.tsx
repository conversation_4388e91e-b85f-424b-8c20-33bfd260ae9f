import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { LineChart, BarChart } from 'react-native-chart-kit';
import { ChartData, UsageRecord } from '../types';

interface UsageChartProps {
  data: UsageRecord[];
  chartType: 'line' | 'area' | 'bar';
  period: 'week' | 'month';
  color: string;
  gradient?: string[];
}

const { width } = Dimensions.get('window');
const chartWidth = width - 60;

const UsageChart: React.FC<UsageChartProps> = ({
  data,
  chartType,
  period,
  color,
  gradient,
}) => {
  // Transform usage data for chart
  const transformDataForChart = () => {
    if (!data || data.length === 0) {
      return {
        labels: [],
        datasets: [{ data: [] }],
      };
    }

    const records = data.slice(0, period === 'week' ? 7 : 30).reverse();
    const labels = records.map(record => {
      const date = new Date(record.record_date);
      return period === 'week'
        ? date.toLocaleDateString('en', { weekday: 'short' })
        : date.getDate().toString();
    });

    const chartData = records.map(record => record.usage_amount);

    return {
      labels,
      datasets: [
        {
          data: chartData,
          color: (opacity = 1) => color + Math.round(opacity * 255).toString(16).padStart(2, '0'),
          strokeWidth: 3,
        },
      ],
    };
  };

  const chartData = transformDataForChart();

  if (chartData.datasets[0].data.length === 0) {
    return (
      <View style={styles.emptyChart}>
        <Text style={styles.emptyChartText}>No data available for chart</Text>
        <Text style={styles.emptyChartSubtext}>
          Record some usage to see your patterns
        </Text>
      </View>
    );
  }

  const dataValues = chartData.datasets[0].data;
  const maxValue = Math.max(...dataValues);
  const minValue = Math.min(...dataValues);

  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    decimalPlaces: 1,
    color: (opacity = 1) => color + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    labelColor: (opacity = 1) => `rgba(102, 102, 102, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: color,
    },
  };

  const renderChart = () => {
    const commonProps = {
      data: chartData,
      width: chartWidth,
      height: 200,
      chartConfig,
      bezier: true,
      style: {
        marginVertical: 8,
        borderRadius: 16,
      },
    };

    switch (chartType) {
      case 'area':
        // Use LineChart with filled area effect
        return (
          <LineChart
            {...commonProps}
            withDots={true}
            withShadow={true}
            withInnerLines={true}
            withOuterLines={true}
            fillShadowGradient={color}
            fillShadowGradientOpacity={0.3}
          />
        );

      case 'bar':
        return (
          <BarChart
            {...commonProps}
            yAxisLabel=""
            yAxisSuffix=""
            showValuesOnTopOfBars={true}
            withInnerLines={true}
            fromZero={true}
          />
        );

      default: // line
        return (
          <LineChart
            {...commonProps}
            withDots={true}
            withShadow={true}
            withInnerLines={true}
            withOuterLines={true}
          />
        );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {period === 'week' ? 'Weekly' : 'Monthly'} Usage Pattern
        </Text>
        <Text style={styles.subtitle}>
          {chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart
        </Text>
      </View>
      
      <View style={styles.chartContainer}>
        {renderChart()}
      </View>

      <View style={styles.stats}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Average</Text>
          <Text style={[styles.statValue, { color }]}>
            {(dataValues.reduce((sum, d) => sum + d, 0) / dataValues.length).toFixed(1)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Highest</Text>
          <Text style={[styles.statValue, { color }]}>
            {maxValue.toFixed(1)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Lowest</Text>
          <Text style={[styles.statValue, { color }]}>
            {minValue.toFixed(1)}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 15,
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyChart: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E5E5EA',
    borderStyle: 'dashed',
  },
  emptyChartText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 5,
  },
  emptyChartSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});

export default UsageChart;
