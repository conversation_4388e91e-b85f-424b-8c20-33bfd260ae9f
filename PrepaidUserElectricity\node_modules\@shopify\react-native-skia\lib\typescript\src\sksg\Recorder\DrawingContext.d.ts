import type { <PERSON><PERSON>, <PERSON>k<PERSON>an<PERSON>, <PERSON>k<PERSON><PERSON>r<PERSON><PERSON><PERSON>, <PERSON>k<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>k<PERSON>mageFilter, SkPathEffect } from "../../skia/types";
export declare const createDrawingContext: (Skia: Skia, paintPool: SkPaint[], canvas: SkCanvas) => {
    Skia: Skia;
    canvas: SkCanvas;
    paints: SkPaint[];
    colorFilters: SkColorFilter[];
    shaders: SkShader[];
    imageFilters: SkImageFilter[];
    pathEffects: SkPathEffect[];
    paintDeclarations: SkPaint[];
    paintPool: SkPaint[];
    savePaint: () => void;
    saveBackdropFilter: () => void;
    readonly paint: SkPaint;
    restorePaint: () => SkPaint | undefined;
    materializePaint: () => void;
    getOpacity: () => number;
    setOpacity: (newOpacity: number) => void;
};
export type DrawingContext = ReturnType<typeof createDrawingContext>;
