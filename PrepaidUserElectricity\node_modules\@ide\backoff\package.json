{"name": "@ide/backoff", "version": "1.0.0", "description": "Computes truncated exponential backoff intervals with jitter", "main": "build/backoff.js", "files": ["build"], "scripts": {"clean": "rm -fr build tsconfig.tsbuildinfo", "build": "tsc", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/ide/backoff.git"}, "keywords": ["backoff"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ide/backoff/issues"}, "homepage": "https://github.com/ide/backoff#readme", "jest": {"roots": ["<rootDir>/src/"], "testEnvironment": "node"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/preset-typescript": "^7.3.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.2", "jest": "^24.9.0", "typescript": "^3.5.3"}}