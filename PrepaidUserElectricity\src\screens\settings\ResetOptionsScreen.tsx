import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const ResetOptionsScreen = () => {
  const [showFactoryResetModal, setShowFactoryResetModal] = useState(false);
  const [showDashboardResetModal, setShowDashboardResetModal] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');

  const handleFactoryReset = () => {
    if (confirmationText.toLowerCase() !== 'reset') {
      Alert.alert('Error', 'Please type "RESET" to confirm');
      return;
    }

    Alert.alert(
      'Factory Reset',
      'All data has been cleared. The app will restart.',
      [
        {
          text: 'OK',
          onPress: () => {
            setShowFactoryResetModal(false);
            setConfirmationText('');
            // TODO: Clear all data and navigate to initial setup
          },
        },
      ]
    );
  };

  const handleDashboardReset = () => {
    if (confirmationText.toLowerCase() !== 'reset') {
      Alert.alert('Error', 'Please type "RESET" to confirm');
      return;
    }

    Alert.alert(
      'Dashboard Reset',
      'Dashboard data has been cleared. History remains intact.',
      [
        {
          text: 'OK',
          onPress: () => {
            setShowDashboardResetModal(false);
            setConfirmationText('');
            // TODO: Clear only dashboard data
          },
        },
      ]
    );
  };

  const resetOptions = [
    {
      title: 'Factory Reset',
      subtitle: 'Clear all data and reset app to initial state',
      icon: 'restore',
      color: '#FF3B30',
      description: 'This will permanently delete all your data including purchases, usage records, history, and settings. The app will return to its initial setup state.',
      onPress: () => setShowFactoryResetModal(true),
    },
    {
      title: 'Dashboard Data Reset',
      subtitle: 'Clear dashboard data only, keep history intact',
      icon: 'refresh',
      color: '#FF9500',
      description: 'This will clear current units, usage calculations, and dashboard totals. Your purchase and usage history will be preserved.',
      onPress: () => setShowDashboardResetModal(true),
    },
  ];

  const renderResetModal = (
    visible: boolean,
    onClose: () => void,
    onConfirm: () => void,
    title: string,
    description: string,
    color: string
  ) => (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Icon name="warning" size={40} color={color} />
            <Text style={styles.modalTitle}>{title}</Text>
          </View>
          
          <Text style={styles.modalDescription}>{description}</Text>
          
          <View style={styles.confirmationContainer}>
            <Text style={styles.confirmationLabel}>
              Type "RESET" to confirm:
            </Text>
            <TextInput
              style={styles.confirmationInput}
              value={confirmationText}
              onChangeText={setConfirmationText}
              placeholder="Type RESET here"
              placeholderTextColor="#999"
              autoCapitalize="characters"
            />
          </View>
          
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                onClose();
                setConfirmationText('');
              }}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.confirmButton, { backgroundColor: color }]}
              onPress={onConfirm}
            >
              <Text style={styles.confirmButtonText}>Reset</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Warning Header */}
      <View style={styles.warningContainer}>
        <Icon name="warning" size={24} color="#FF9500" />
        <Text style={styles.warningText}>
          Reset options will permanently modify or delete your data. Please proceed with caution.
        </Text>
      </View>

      {/* Reset Options */}
      <View style={styles.optionsContainer}>
        <Text style={styles.sectionTitle}>Reset Options</Text>
        
        {resetOptions.map((option, index) => (
          <View key={index} style={styles.optionCard}>
            <View style={styles.optionHeader}>
              <View style={[styles.optionIcon, { backgroundColor: option.color + '20' }]}>
                <Icon name={option.icon} size={24} color={option.color} />
              </View>
              <View style={styles.optionContent}>
                <Text style={styles.optionTitle}>{option.title}</Text>
                <Text style={styles.optionSubtitle}>{option.subtitle}</Text>
              </View>
            </View>
            
            <Text style={styles.optionDescription}>{option.description}</Text>
            
            <TouchableOpacity
              style={[styles.resetButton, { backgroundColor: option.color }]}
              onPress={option.onPress}
            >
              <Icon name={option.icon} size={20} color="#fff" />
              <Text style={styles.resetButtonText}>{option.title}</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>

      {/* Data Information */}
      <View style={styles.infoContainer}>
        <Text style={styles.sectionTitle}>Data Information</Text>
        
        <View style={styles.infoItem}>
          <Icon name="storage" size={20} color="#5AC8FA" />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Database Size</Text>
            <Text style={styles.infoValue}>2.4 MB</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Icon name="shopping-cart" size={20} color="#34C759" />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Total Purchases</Text>
            <Text style={styles.infoValue}>15 records</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Icon name="trending-up" size={20} color="#FF9500" />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Usage Records</Text>
            <Text style={styles.infoValue}>42 records</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Icon name="history" size={20} color="#007AFF" />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>History Entries</Text>
            <Text style={styles.infoValue}>57 records</Text>
          </View>
        </View>
      </View>

      {/* Backup Information */}
      <View style={styles.backupContainer}>
        <Text style={styles.sectionTitle}>
          <Icon name="backup" size={18} color="#5AC8FA" /> Backup Information
        </Text>
        
        <Text style={styles.backupText}>
          Currently, this app stores data locally on your device. Before performing a factory reset, consider:
        </Text>
        
        <View style={styles.backupList}>
          <Text style={styles.backupItem}>• Taking screenshots of important data</Text>
          <Text style={styles.backupItem}>• Noting down your current settings</Text>
          <Text style={styles.backupItem}>• Recording your current unit values</Text>
          <Text style={styles.backupItem}>• Saving your cost per unit configuration</Text>
        </View>
        
        <View style={styles.futureFeatureNote}>
          <Icon name="info" size={16} color="#666" />
          <Text style={styles.futureFeatureText}>
            Cloud backup and restore features will be available in future updates.
          </Text>
        </View>
      </View>

      {/* Modals */}
      {renderResetModal(
        showFactoryResetModal,
        () => setShowFactoryResetModal(false),
        handleFactoryReset,
        'Factory Reset',
        'This will permanently delete ALL your data including purchases, usage records, history, and settings. This action cannot be undone.',
        '#FF3B30'
      )}

      {renderResetModal(
        showDashboardResetModal,
        () => setShowDashboardResetModal(false),
        handleDashboardReset,
        'Dashboard Reset',
        'This will clear your current units, usage calculations, and dashboard totals. Your purchase and usage history will be preserved.',
        '#FF9500'
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  warningContainer: {
    backgroundColor: '#FFF3CD',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: '#FFEAA7',
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: '#856404',
    marginLeft: 10,
    lineHeight: 20,
  },
  optionsContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 20,
  },
  optionCard: {
    backgroundColor: '#F8F9FA',
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  optionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 15,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  infoContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  infoContent: {
    flex: 1,
    marginLeft: 15,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1D1D1F',
  },
  infoValue: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  backupContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    marginBottom: 30,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backupText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 15,
  },
  backupList: {
    marginBottom: 15,
  },
  backupItem: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
    marginBottom: 4,
  },
  futureFeatureNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F0F9FF',
    padding: 12,
    borderRadius: 8,
  },
  futureFeatureText: {
    flex: 1,
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1D1D1F',
    marginTop: 10,
  },
  modalDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    textAlign: 'center',
    marginBottom: 20,
  },
  confirmationContainer: {
    marginBottom: 20,
  },
  confirmationLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1D1D1F',
    marginBottom: 8,
  },
  confirmationInput: {
    borderWidth: 2,
    borderColor: '#FF3B30',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  confirmButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default ResetOptionsScreen;
