import type { HostConfig } from "react-reconciler";
import type { NodeType } from "../dom/types";
import type { Node } from "./Node";
import type { Container } from "./Container";
export declare const debug: (...args: Parameters<typeof console.log>) => void;
type Instance = Node;
type Props = object;
type TextInstance = Node;
type SuspenseInstance = Instance;
type HydratableInstance = Instance;
type PublicInstance = Instance;
type HostContext = object;
type UpdatePayload = Container;
type ChildSet = Node[];
type TimeoutHandle = NodeJS.Timeout;
type NoTimeout = -1;
type SkiaHostConfig = HostConfig<NodeType, Props, Container, Instance, TextInstance, SuspenseInstance, HydratableInstance, PublicInstance, HostContext, UpdatePayload, ChildSet, TimeoutHandle, NoTimeout>;
export declare const sksgHostConfig: SkiaHostConfig;
export {};
