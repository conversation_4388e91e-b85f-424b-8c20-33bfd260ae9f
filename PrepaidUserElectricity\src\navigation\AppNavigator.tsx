import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import screens (we'll create these next)
import DashboardScreen from '../screens/DashboardScreen';
import PurchasesScreen from '../screens/PurchasesScreen';
import UsageScreen from '../screens/UsageScreen';
import HistoryScreen from '../screens/HistoryScreen';
import SettingsScreen from '../screens/SettingsScreen';
import GeneralSettingsScreen from '../screens/settings/GeneralSettingsScreen';
import AppearanceSettingsScreen from '../screens/settings/AppearanceSettingsScreen';
import ResetOptionsScreen from '../screens/settings/ResetOptionsScreen';
import InitialSetupScreen from '../screens/InitialSetupScreen';

import { RootStackParamList } from '../types';

const Drawer = createDrawerNavigator();
const Stack = createStackNavigator<RootStackParamList>();

// Custom Drawer Content
const CustomDrawerContent = ({ navigation, state }: any) => {
  const menuItems = [
    { name: 'Dashboard', icon: 'dashboard', screen: 'Dashboard' },
    { name: 'Purchases', icon: 'shopping-cart', screen: 'Purchases' },
    { name: 'Usage', icon: 'trending-up', screen: 'Usage' },
    { name: 'History', icon: 'history', screen: 'History' },
    { name: 'Settings', icon: 'settings', screen: 'Settings' },
  ];

  return (
    <View style={styles.drawerContainer}>
      {/* App Logo and Title */}
      <View style={styles.drawerHeader}>
        <Icon name="flash-on" size={40} color="#007AFF" />
        <Text style={styles.appTitle}>PREPAID USER</Text>
        <Text style={styles.appSubtitle}>ELECTRICITY</Text>
      </View>

      {/* Menu Items */}
      <View style={styles.menuContainer}>
        {menuItems.map((item, index) => {
          const isActive = state.routeNames[state.index] === item.screen;
          return (
            <TouchableOpacity
              key={index}
              style={[styles.menuItem, isActive && styles.activeMenuItem]}
              onPress={() => navigation.navigate(item.screen)}
            >
              <Icon 
                name={item.icon} 
                size={24} 
                color={isActive ? '#007AFF' : '#666'} 
              />
              <Text style={[styles.menuText, isActive && styles.activeMenuText]}>
                {item.name}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* App Version */}
      <View style={styles.drawerFooter}>
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </View>
    </View>
  );
};

// Main Drawer Navigator
const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerStyle: {
          backgroundColor: '#007AFF',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        drawerStyle: {
          backgroundColor: '#f8f9fa',
          width: 280,
        },
      }}
    >
      <Drawer.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{
          title: 'Dashboard',
          headerLeft: ({ onPress }) => (
            <TouchableOpacity onPress={onPress} style={styles.headerButton}>
              <Icon name="menu" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      <Drawer.Screen 
        name="Purchases" 
        component={PurchasesScreen}
        options={{
          title: 'Purchases',
          headerLeft: ({ onPress }) => (
            <TouchableOpacity onPress={onPress} style={styles.headerButton}>
              <Icon name="menu" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      <Drawer.Screen 
        name="Usage" 
        component={UsageScreen}
        options={{
          title: 'Usage Tracking',
          headerLeft: ({ onPress }) => (
            <TouchableOpacity onPress={onPress} style={styles.headerButton}>
              <Icon name="menu" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      <Drawer.Screen 
        name="History" 
        component={HistoryScreen}
        options={{
          title: 'History',
          headerLeft: ({ onPress }) => (
            <TouchableOpacity onPress={onPress} style={styles.headerButton}>
              <Icon name="menu" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      <Drawer.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          title: 'Settings',
          headerLeft: ({ onPress }) => (
            <TouchableOpacity onPress={onPress} style={styles.headerButton}>
              <Icon name="menu" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
    </Drawer.Navigator>
  );
};

// Main Stack Navigator
const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Main"
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="InitialSetup" component={InitialSetupScreen} />
        <Stack.Screen name="Main" component={DrawerNavigator} />
        <Stack.Screen 
          name="GeneralSettings" 
          component={GeneralSettingsScreen}
          options={{
            headerShown: true,
            title: 'General Settings',
            headerStyle: { backgroundColor: '#007AFF' },
            headerTintColor: '#fff',
          }}
        />
        <Stack.Screen 
          name="AppearanceSettings" 
          component={AppearanceSettingsScreen}
          options={{
            headerShown: true,
            title: 'Appearance',
            headerStyle: { backgroundColor: '#007AFF' },
            headerTintColor: '#fff',
          }}
        />
        <Stack.Screen 
          name="ResetOptions" 
          component={ResetOptionsScreen}
          options={{
            headerShown: true,
            title: 'Reset Options',
            headerStyle: { backgroundColor: '#007AFF' },
            headerTintColor: '#fff',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  drawerHeader: {
    backgroundColor: '#fff',
    padding: 20,
    paddingTop: 50,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  appTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    marginTop: 10,
  },
  appSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  menuContainer: {
    flex: 1,
    paddingTop: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginHorizontal: 10,
    borderRadius: 8,
  },
  activeMenuItem: {
    backgroundColor: '#e3f2fd',
  },
  menuText: {
    fontSize: 16,
    marginLeft: 15,
    color: '#333',
  },
  activeMenuText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  drawerFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    alignItems: 'center',
  },
  versionText: {
    fontSize: 12,
    color: '#999',
  },
  headerButton: {
    marginLeft: 15,
    padding: 5,
  },
});

export default AppNavigator;
