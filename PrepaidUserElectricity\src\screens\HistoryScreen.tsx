import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const HistoryScreen = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week');
  const [selectedType, setSelectedType] = useState<'all' | 'purchase' | 'usage'>('all');

  // Mock data - will be replaced with database data
  const historyData = [
    {
      id: 1,
      type: 'usage',
      amount: 12.5,
      unit: 'kWh',
      date: '2024-01-15T18:00:00Z',
      description: 'Usage: 12.5 kWh (87.5 → 75.0)',
    },
    {
      id: 2,
      type: 'purchase',
      amount: 166.67,
      unit: 'kWh',
      currencyAmount: 25.00,
      currency: 'USD',
      date: '2024-01-15T14:30:00Z',
      description: 'Purchase: $25.00 for 166.67 kWh',
    },
    {
      id: 3,
      type: 'usage',
      amount: 15.2,
      unit: 'kWh',
      date: '2024-01-14T20:30:00Z',
      description: 'Usage: 15.2 kWh (102.7 → 87.5)',
    },
    {
      id: 4,
      type: 'purchase',
      amount: 333.33,
      unit: 'kWh',
      currencyAmount: 50.00,
      currency: 'USD',
      date: '2024-01-14T10:15:00Z',
      description: 'Purchase: $50.00 for 333.33 kWh',
    },
  ];

  const totals = {
    week: {
      purchases: { currency: 75.00, units: 500.00 },
      usage: { units: 85.5 },
    },
    month: {
      purchases: { currency: 275.00, units: 1833.33 },
      usage: { units: 320.8 },
    },
  };

  const filteredData = historyData.filter(item => {
    if (selectedType !== 'all' && item.type !== selectedType) {
      return false;
    }
    // Add date filtering logic here based on selectedPeriod
    return true;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderHistoryItem = ({ item }: { item: any }) => (
    <View style={styles.historyItem}>
      <View style={styles.historyHeader}>
        <Icon 
          name={item.type === 'purchase' ? 'shopping-cart' : 'trending-up'} 
          size={20} 
          color={item.type === 'purchase' ? '#34C759' : '#FF9500'} 
        />
        <View style={styles.historyInfo}>
          <Text style={styles.historyType}>
            {item.type === 'purchase' ? 'Purchase' : 'Usage'}
          </Text>
          <Text style={styles.historyDate}>{formatDate(item.date)}</Text>
        </View>
        <View style={styles.historyAmount}>
          {item.type === 'purchase' ? (
            <>
              <Text style={styles.currencyAmount}>${item.currencyAmount?.toFixed(2)}</Text>
              <Text style={styles.unitAmount}>{item.amount.toFixed(2)} {item.unit}</Text>
            </>
          ) : (
            <Text style={styles.usageAmount}>{item.amount.toFixed(2)} {item.unit}</Text>
          )}
        </View>
      </View>
      <Text style={styles.historyDescription}>{item.description}</Text>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Filter Controls */}
      <View style={styles.filterContainer}>
        <Text style={styles.sectionTitle}>Filter Options</Text>
        
        {/* Period Filter */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Period:</Text>
          <View style={styles.filterButtons}>
            {['week', 'month', 'all'].map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.filterButton,
                  selectedPeriod === period && styles.activeFilterButton,
                ]}
                onPress={() => setSelectedPeriod(period as any)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    selectedPeriod === period && styles.activeFilterButtonText,
                  ]}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Type Filter */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Type:</Text>
          <View style={styles.filterButtons}>
            {['all', 'purchase', 'usage'].map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.filterButton,
                  selectedType === type && styles.activeFilterButton,
                ]}
                onPress={() => setSelectedType(type as any)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    selectedType === type && styles.activeFilterButtonText,
                  ]}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Totals Summary */}
      <View style={styles.totalsContainer}>
        <Text style={styles.sectionTitle}>
          {selectedPeriod === 'week' ? 'Weekly' : selectedPeriod === 'month' ? 'Monthly' : 'All Time'} Totals
        </Text>
        
        <View style={styles.totalsGrid}>
          <View style={styles.totalCard}>
            <Icon name="shopping-cart" size={24} color="#34C759" />
            <Text style={styles.totalLabel}>Purchases</Text>
            <Text style={styles.totalCurrency}>
              ${totals[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.purchases.currency.toFixed(2)}
            </Text>
            <Text style={styles.totalUnits}>
              {totals[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.purchases.units.toFixed(2)} kWh
            </Text>
          </View>
          
          <View style={styles.totalCard}>
            <Icon name="trending-up" size={24} color="#FF9500" />
            <Text style={styles.totalLabel}>Usage</Text>
            <Text style={styles.totalUsage}>
              {totals[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.usage.units.toFixed(2)} kWh
            </Text>
            <Text style={styles.totalRemaining}>
              Remaining: {(totals[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.purchases.units - totals[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.usage.units).toFixed(2)} kWh
            </Text>
          </View>
        </View>
      </View>

      {/* History List */}
      <View style={styles.historyContainer}>
        <Text style={styles.sectionTitle}>Transaction History</Text>
        <FlatList
          data={filteredData}
          renderItem={renderHistoryItem}
          keyExtractor={(item) => item.id.toString()}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  filterContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 15,
  },
  filterGroup: {
    marginBottom: 15,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 8,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F2F2F7',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  activeFilterButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  totalsContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  totalsGrid: {
    flexDirection: 'row',
    gap: 15,
  },
  totalCard: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginTop: 8,
    marginBottom: 8,
  },
  totalCurrency: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#34C759',
    marginBottom: 4,
  },
  totalUnits: {
    fontSize: 12,
    color: '#666',
  },
  totalUsage: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF9500',
    marginBottom: 4,
  },
  totalRemaining: {
    fontSize: 12,
    color: '#666',
  },
  historyContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    marginBottom: 30,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  historyItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
    paddingVertical: 15,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyInfo: {
    flex: 1,
    marginLeft: 12,
  },
  historyType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  historyDate: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  historyAmount: {
    alignItems: 'flex-end',
  },
  currencyAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#34C759',
  },
  unitAmount: {
    fontSize: 12,
    color: '#666',
  },
  usageAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF9500',
  },
  historyDescription: {
    fontSize: 14,
    color: '#666',
    marginLeft: 32,
    fontStyle: 'italic',
  },
});

export default HistoryScreen;
