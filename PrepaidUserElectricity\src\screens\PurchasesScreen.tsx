import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  FlatList,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { purchaseService } from '../services/databaseService';
import { Purchase } from '../types';
import { getAppConfig } from '../storage/asyncStorage';

const PurchasesScreen = () => {
  const [currencyAmount, setCurrencyAmount] = useState('');
  const [unitAmount, setUnitAmount] = useState('');
  const [costPerUnit, setCostPerUnit] = useState(0.15);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [selectedUnit, setSelectedUnit] = useState('kWh');
  const [recentPurchases, setRecentPurchases] = useState<Purchase[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadAppConfig();
    loadRecentPurchases();
  }, []);

  const loadAppConfig = async () => {
    try {
      const config = await getAppConfig();
      setCostPerUnit(config.costPerUnit);
      setSelectedCurrency(config.selectedCurrency.code);
      setSelectedUnit(config.selectedUnit.symbol);
    } catch (error) {
      console.error('Error loading app config:', error);
    }
  };

  const loadRecentPurchases = async () => {
    try {
      setIsLoading(true);
      const purchases = await purchaseService.getAllPurchases();
      setRecentPurchases(purchases.slice(0, 10)); // Show last 10 purchases
    } catch (error) {
      console.error('Error loading purchases:', error);
      Alert.alert('Error', 'Failed to load recent purchases');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadRecentPurchases();
    setRefreshing(false);
  };

  // Live preview calculation
  const calculatePreview = () => {
    const currency = parseFloat(currencyAmount) || 0;
    const calculatedUnits = currency / costPerUnit;
    return {
      currency,
      units: calculatedUnits,
      costPerUnit,
    };
  };

  const preview = calculatePreview();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return `Today, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays === 2) {
      return `Yesterday, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString() + ', ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  const renderPurchaseItem = ({ item }: { item: Purchase }) => (
    <View style={styles.purchaseItem}>
      <View style={styles.purchaseHeader}>
        <Icon name="shopping-cart" size={20} color="#007AFF" />
        <Text style={styles.purchaseDate}>{formatDate(item.purchase_date)}</Text>
      </View>
      <View style={styles.purchaseDetails}>
        <Text style={styles.purchaseAmount}>
          {item.currency_type} {item.currency_amount.toFixed(2)} → {item.unit_amount.toFixed(2)} {item.unit_type}
        </Text>
        <Text style={styles.purchaseRate}>
          Rate: {item.currency_type} {item.cost_per_unit.toFixed(3)}/{item.unit_type}
        </Text>
      </View>
    </View>
  );

  const handleSavePurchase = async () => {
    if (!currencyAmount || parseFloat(currencyAmount) <= 0) {
      Alert.alert('Error', 'Please enter a valid currency amount');
      return;
    }

    try {
      setIsLoading(true);

      const purchase: Omit<Purchase, 'id'> = {
        currency_amount: parseFloat(currencyAmount),
        unit_amount: preview.units,
        currency_type: selectedCurrency,
        unit_type: selectedUnit,
        cost_per_unit: costPerUnit,
        purchase_date: new Date().toISOString(),
        notes: `Purchase of ${preview.units.toFixed(2)} ${selectedUnit} for ${selectedCurrency} ${currencyAmount}`,
      };

      await purchaseService.addPurchase(purchase);

      Alert.alert(
        'Purchase Saved',
        `Saved: ${currencyAmount} ${selectedCurrency} for ${preview.units.toFixed(2)} ${selectedUnit}`,
        [
          {
            text: 'OK',
            onPress: () => {
              setCurrencyAmount('');
              setUnitAmount('');
              loadRecentPurchases(); // Refresh the list
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error saving purchase:', error);
      Alert.alert('Error', 'Failed to save purchase. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Input Section */}
      <View style={styles.inputContainer}>
        <Text style={styles.sectionTitle}>Add New Purchase</Text>
        
        {/* Currency Input */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>
            <Icon name="attach-money" size={16} color="#007AFF" /> Currency Amount ({selectedCurrency})
          </Text>
          <TextInput
            style={styles.input}
            value={currencyAmount}
            onChangeText={setCurrencyAmount}
            placeholder="Enter amount spent"
            keyboardType="numeric"
            placeholderTextColor="#999"
          />
        </View>

        {/* Live Preview */}
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>
            <Icon name="visibility" size={16} color="#34C759" /> Live Preview
          </Text>
          <View style={styles.previewContent}>
            <View style={styles.previewRow}>
              <Text style={styles.previewLabel}>Currency Amount:</Text>
              <Text style={styles.previewValue}>
                {selectedCurrency} {preview.currency.toFixed(2)}
              </Text>
            </View>
            <View style={styles.previewRow}>
              <Text style={styles.previewLabel}>Units Purchased:</Text>
              <Text style={styles.previewValue}>
                {preview.units.toFixed(2)} {selectedUnit}
              </Text>
            </View>
            <View style={styles.previewRow}>
              <Text style={styles.previewLabel}>Cost per Unit:</Text>
              <Text style={styles.previewValue}>
                {selectedCurrency} {costPerUnit.toFixed(3)}
              </Text>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity style={styles.saveButton} onPress={handleSavePurchase}>
          <Icon name="save" size={20} color="#fff" />
          <Text style={styles.saveButtonText}>Save Purchase</Text>
        </TouchableOpacity>
      </View>

      {/* Recent Purchases */}
      <View style={styles.recentContainer}>
        <Text style={styles.sectionTitle}>Recent Purchases</Text>
        {recentPurchases.length > 0 ? (
          <FlatList
            data={recentPurchases}
            renderItem={renderPurchaseItem}
            keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          />
        ) : (
          <View style={styles.emptyState}>
            <Icon name="shopping-cart" size={48} color="#C7C7CC" />
            <Text style={styles.emptyStateText}>No purchases yet</Text>
            <Text style={styles.emptyStateSubtext}>
              Add your first purchase above to get started
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#F8F9FA',
  },
  previewContainer: {
    backgroundColor: '#F0F9FF',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#34C759',
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#34C759',
    marginBottom: 10,
  },
  previewContent: {
    gap: 8,
  },
  previewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  previewLabel: {
    fontSize: 14,
    color: '#666',
  },
  previewValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  saveButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    gap: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  recentContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  purchaseItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
    paddingVertical: 15,
  },
  purchaseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  purchaseDate: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  purchaseDetails: {
    marginLeft: 28,
  },
  purchaseAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 4,
  },
  purchaseRate: {
    fontSize: 14,
    color: '#666',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E5E5EA',
    borderStyle: 'dashed',
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginTop: 15,
    marginBottom: 5,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});

export default PurchasesScreen;
