{"version": 3, "file": "backoff.js", "sourceRoot": "", "sources": ["../src/backoff.ts"], "names": [], "mappings": ";;;;;AAAA,oDAA4B;AAE5B,SAAgB,0BAA0B,CACxC,cAAsB,EACtB,kBAA0B,EAC1B,EACE,UAAU,GAAG,GAAG,EAChB,mBAAmB,GAAG,IAAI,EAC1B,UAAU,GAAG,cAAc,EAC3B,UAAU,GAAG,QAAQ,GACtB,GAAG,EAAE;IAEN,gBAAM,CAAC,cAAc,GAAG,CAAC,EAAE,+CAA+C,CAAC,CAAC;IAC5E,gBAAM,CAAC,kBAAkB,IAAI,CAAC,EAAE,+CAA+C,CAAC,CAAC;IACjF,gBAAM,CAAC,UAAU,IAAI,CAAC,EAAE,2DAA2D,CAAC,CAAC;IACrF,gBAAM,CACJ,CAAC,IAAI,mBAAmB,IAAI,mBAAmB,IAAI,CAAC,EACpD,6DAA6D,CAC9D,CAAC;IACF,gBAAM,CAAC,UAAU,IAAI,CAAC,EAAE,+CAA+C,CAAC,CAAC;IAEzE,IAAI,WAAW,GAAG,cAAc,GAAG,UAAU,IAAI,kBAAkB,CAAC;IACpE,iFAAiF;IACjF,IAAI,YAAY,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,mBAAmB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IACrF,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,YAAY,EAAE,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC;AAChF,CAAC;AAvBD,gEAuBC", "sourcesContent": ["import assert from 'assert';\n\nexport function computeNextBackoffInterval(\n  initialBackoff: number,\n  previousRetryCount: number,\n  {\n    multiplier = 1.5,\n    randomizationFactor = 0.25,\n    minBackoff = initialBackoff,\n    maxBackoff = Infinity,\n  } = {},\n): number {\n  assert(initialBackoff > 0, `The initial backoff interval must be positive`);\n  assert(previousRetryCount >= 0, `The previous retry count must not be negative`);\n  assert(multiplier >= 1, `The backoff multiplier must be greater than or equal to 1`);\n  assert(\n    0 <= randomizationFactor && randomizationFactor <= 1,\n    `The randomization factor must be between 0 and 1, inclusive`,\n  );\n  assert(minBackoff >= 0, `The minimum backoff interval must be positive`);\n\n  let nextBackoff = initialBackoff * multiplier ** previousRetryCount;\n  // Apply jitter within the negative to positive range of the randomization factor\n  let jitterFactor = 1 - randomizationFactor + 2 * randomizationFactor * Math.random();\n  return Math.min(Math.max(nextBackoff * jitterFactor, minBackoff), maxBackoff);\n}\n"]}