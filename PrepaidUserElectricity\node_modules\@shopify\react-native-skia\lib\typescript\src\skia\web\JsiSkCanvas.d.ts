import type { <PERSON>vas, CanvasKit } from "canvaskit-wasm";
import { type BlendMode, type <PERSON>lipOp, type FilterMode, type MipmapMode, type PointMode, type <PERSON>LayerFlag, type ImageInfo, type SkCanvas, type SkColor, type Sk<PERSON>ont, type SkImage, type SkImageFilter, type SkMatrix, type SkPaint, type SkPath, type SkPicture, type SkPoint, type SkRect, type InputRRect, type SkSVG, type SkTextBlob, type SkVertices, type SkRSXform, type CubicResampler, type FilterOptions } from "../types";
import { HostObject } from "./Host";
export declare class JsiSkCanvas extends HostObject<Canvas, "Canvas"> implements SkCanvas {
    constructor(CanvasKit: CanvasKit, ref: Canvas);
    dispose: () => void;
    drawRect(rect: SkRect, paint: SkPaint): void;
    drawImage(image: SkImage, x: number, y: number, paint?: SkPaint): void;
    drawImageRect(img: SkImage, src: SkRect, dest: SkRect, paint: SkPaint, fastSample?: boolean): void;
    drawImageCubic(img: SkImage, left: number, top: number, B: number, C: number, paint?: SkPaint | null): void;
    drawImageOptions(img: SkImage, left: number, top: number, fm: FilterMode, mm: MipmapMode, paint?: SkPaint | null): void;
    drawImageNine(img: SkImage, center: SkRect, dest: SkRect, filter: FilterMode, paint?: SkPaint | null): void;
    drawImageRectCubic(img: SkImage, src: SkRect, dest: SkRect, B: number, C: number, paint?: SkPaint | null): void;
    drawImageRectOptions(img: SkImage, src: SkRect, dest: SkRect, fm: FilterMode, mm: MipmapMode, paint?: SkPaint | null): void;
    drawPaint(paint: SkPaint): void;
    drawLine(x0: number, y0: number, x1: number, y1: number, paint: SkPaint): void;
    drawCircle(cx: number, cy: number, radius: number, paint: SkPaint): void;
    drawVertices(verts: SkVertices, mode: BlendMode, paint: SkPaint): void;
    drawPatch(cubics: SkPoint[], colors?: SkColor[] | null, texs?: SkPoint[] | null, mode?: BlendMode | null, paint?: SkPaint): void;
    restoreToCount(saveCount: number): void;
    drawPoints(mode: PointMode, points: SkPoint[], paint: SkPaint): void;
    drawArc(oval: SkRect, startAngle: number, sweepAngle: number, useCenter: boolean, paint: SkPaint): void;
    drawRRect(rrect: InputRRect, paint: SkPaint): void;
    drawDRRect(outer: InputRRect, inner: InputRRect, paint: SkPaint): void;
    drawOval(oval: SkRect, paint: SkPaint): void;
    drawPath(path: SkPath, paint: SkPaint): void;
    drawText(str: string, x: number, y: number, paint: SkPaint, font: SkFont): void;
    drawTextBlob(blob: SkTextBlob, x: number, y: number, paint: SkPaint): void;
    drawGlyphs(glyphs: number[], positions: SkPoint[], x: number, y: number, font: SkFont, paint: SkPaint): void;
    drawSvg(svg: SkSVG, _width?: number, _height?: number): void;
    save(): number;
    saveLayer(paint?: SkPaint, bounds?: SkRect | null, backdrop?: SkImageFilter | null, flags?: SaveLayerFlag): number;
    restore(): void;
    rotate(rotationInDegrees: number, rx: number, ry: number): void;
    scale(sx: number, sy: number): void;
    skew(sx: number, sy: number): void;
    translate(dx: number, dy: number): void;
    drawColor(color: SkColor, blendMode?: BlendMode): void;
    clear(color: SkColor): void;
    clipPath(path: SkPath, op: ClipOp, doAntiAlias: boolean): void;
    clipRect(rect: SkRect, op: ClipOp, doAntiAlias: boolean): void;
    clipRRect(rrect: InputRRect, op: ClipOp, doAntiAlias: boolean): void;
    concat(m: SkMatrix | number[]): void;
    drawPicture(skp: SkPicture): void;
    drawAtlas(atlas: SkImage, srcs: SkRect[], dsts: SkRSXform[], paint: SkPaint, blendMode?: BlendMode, colors?: SkColor[], sampling?: CubicResampler | FilterOptions): void;
    readPixels(srcX: number, srcY: number, imageInfo: ImageInfo): Float32Array<ArrayBufferLike> | Uint8Array<ArrayBufferLike> | null;
}
