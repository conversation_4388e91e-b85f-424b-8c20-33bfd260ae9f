import type { Can<PERSON><PERSON>it, TypefaceFontProvider } from "canvaskit-wasm";
import type { SkTypefaceFontProvider } from "../types/Paragraph/TypefaceFontProvider";
import type { FontStyle, SkTypeface } from "../types";
import { HostObject } from "./Host";
export declare class JsiSkTypeface<PERSON>ontProvider extends HostObject<TypefaceFontProvider, "FontMgr"> implements SkTypefaceFontProvider {
    private allocatedPointers;
    constructor(CanvasKit: CanvasKit, ref: TypefaceFontProvider);
    matchFamilyStyle(_name: string, _style: FontStyle): SkTypeface;
    countFamilies(): number;
    getFamilyName(index: number): string;
    registerFont(typeface: SkTypeface, familyName: string): void;
    dispose(): void;
}
