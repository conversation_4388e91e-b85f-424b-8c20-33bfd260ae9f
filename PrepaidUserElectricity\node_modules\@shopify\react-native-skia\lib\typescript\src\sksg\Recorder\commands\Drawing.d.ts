import type { AtlasProps, CircleProps, DiffRectProps, DrawingNodeProps, GlyphsProps, ImageProps, ImageSVGProps, LineProps, OvalProps, ParagraphProps, PatchProps, PathProps, PictureProps, PointsProps, RectProps, RoundedRectProps, TextBlobProps, TextPathProps, TextProps, VerticesProps } from "../../../dom/types";
import type { DrawingContext } from "../DrawingContext";
export declare const drawLine: (ctx: DrawingContext, props: LineProps) => void;
export declare const drawOval: (ctx: DrawingContext, props: OvalProps) => void;
export declare const drawImage: (ctx: DrawingContext, props: ImageProps) => void;
export declare const drawPoints: (ctx: DrawingContext, props: PointsProps) => void;
export declare const drawVertices: (ctx: DrawingContext, props: VerticesProps) => void;
export declare const drawDiffRect: (ctx: DrawingContext, props: DiffRectProps) => void;
export declare const drawTextPath: (ctx: DrawingContext, props: TextPathProps) => void;
export declare const drawText: (ctx: DrawingContext, props: TextProps) => void;
export declare const drawPatch: (ctx: DrawingContext, props: PatchProps) => void;
export declare const drawPath: (ctx: DrawingContext, props: PathProps) => void;
export declare const drawRect: (ctx: DrawingContext, props: RectProps) => void;
export declare const drawRRect: (ctx: DrawingContext, props: RoundedRectProps) => void;
export declare const drawTextBlob: (ctx: DrawingContext, props: TextBlobProps) => void;
export declare const drawGlyphs: (ctx: DrawingContext, props: GlyphsProps) => void;
export declare const drawImageSVG: (ctx: DrawingContext, props: ImageSVGProps) => void;
export declare const drawParagraph: (ctx: DrawingContext, props: ParagraphProps) => void;
export declare const drawPicture: (ctx: DrawingContext, props: PictureProps) => void;
export declare const drawAtlas: (ctx: DrawingContext, props: AtlasProps) => void;
export declare const drawCircle: (ctx: DrawingContext, props: CircleProps) => void;
export declare const drawFill: (ctx: DrawingContext, _props: DrawingNodeProps) => void;
