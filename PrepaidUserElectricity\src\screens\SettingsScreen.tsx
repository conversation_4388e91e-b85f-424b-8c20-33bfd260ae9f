import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

const SettingsScreen = () => {
  const navigation = useNavigation();

  const settingsOptions = [
    {
      title: 'General Settings',
      subtitle: 'Unit costs, currencies, thresholds, notifications',
      icon: 'settings',
      color: '#007AFF',
      onPress: () => navigation.navigate('GeneralSettings' as never),
    },
    {
      title: 'Appearance',
      subtitle: 'Themes, fonts, colors, and visual preferences',
      icon: 'palette',
      color: '#FF9500',
      onPress: () => navigation.navigate('AppearanceSettings' as never),
    },
    {
      title: 'Reset Options',
      subtitle: 'Factory reset, dashboard reset, and data management',
      icon: 'refresh',
      color: '#FF3B30',
      onPress: () => navigation.navigate('ResetOptions' as never),
    },
  ];

  return (
    <ScrollView style={styles.container}>
      {/* App Info Header */}
      <View style={styles.headerContainer}>
        <View style={styles.logoContainer}>
          <Icon name="flash-on" size={60} color="#007AFF" />
        </View>
        <Text style={styles.appTitle}>PREPAID USER - ELECTRICITY</Text>
        <Text style={styles.appVersion}>Version 1.0.0</Text>
        <Text style={styles.appDescription}>
          Track your prepaid electricity usage and purchases with ease
        </Text>
      </View>

      {/* Settings Options */}
      <View style={styles.settingsContainer}>
        <Text style={styles.sectionTitle}>Settings</Text>
        
        {settingsOptions.map((option, index) => (
          <TouchableOpacity
            key={index}
            style={styles.settingItem}
            onPress={option.onPress}
          >
            <View style={[styles.iconContainer, { backgroundColor: option.color + '20' }]}>
              <Icon name={option.icon} size={24} color={option.color} />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>{option.title}</Text>
              <Text style={styles.settingSubtitle}>{option.subtitle}</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#C7C7CC" />
          </TouchableOpacity>
        ))}
      </View>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>Quick Stats</Text>
        
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Icon name="shopping-cart" size={24} color="#34C759" />
            <Text style={styles.statValue}>15</Text>
            <Text style={styles.statLabel}>Total Purchases</Text>
          </View>
          
          <View style={styles.statCard}>
            <Icon name="trending-up" size={24} color="#FF9500" />
            <Text style={styles.statValue}>42</Text>
            <Text style={styles.statLabel}>Usage Records</Text>
          </View>
          
          <View style={styles.statCard}>
            <Icon name="history" size={24} color="#5AC8FA" />
            <Text style={styles.statValue}>57</Text>
            <Text style={styles.statLabel}>Total Entries</Text>
          </View>
        </View>
      </View>

      {/* App Information */}
      <View style={styles.infoContainer}>
        <Text style={styles.sectionTitle}>About</Text>
        
        <View style={styles.infoItem}>
          <Icon name="info" size={20} color="#007AFF" />
          <View style={styles.infoContent}>
            <Text style={styles.infoTitle}>App Version</Text>
            <Text style={styles.infoValue}>1.0.0 (Build 1)</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Icon name="storage" size={20} color="#34C759" />
          <View style={styles.infoContent}>
            <Text style={styles.infoTitle}>Database Size</Text>
            <Text style={styles.infoValue}>2.4 MB</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Icon name="update" size={20} color="#FF9500" />
          <View style={styles.infoContent}>
            <Text style={styles.infoTitle}>Last Updated</Text>
            <Text style={styles.infoValue}>January 15, 2024</Text>
          </View>
        </View>
      </View>

      {/* Support Section */}
      <View style={styles.supportContainer}>
        <Text style={styles.sectionTitle}>Support</Text>
        
        <TouchableOpacity style={styles.supportItem}>
          <Icon name="help" size={20} color="#5AC8FA" />
          <Text style={styles.supportText}>Help & FAQ</Text>
          <Icon name="chevron-right" size={20} color="#C7C7CC" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.supportItem}>
          <Icon name="feedback" size={20} color="#FF9500" />
          <Text style={styles.supportText}>Send Feedback</Text>
          <Icon name="chevron-right" size={20} color="#C7C7CC" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.supportItem}>
          <Icon name="star" size={20} color="#FFCC02" />
          <Text style={styles.supportText}>Rate App</Text>
          <Icon name="chevron-right" size={20} color="#C7C7CC" />
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  headerContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F0F9FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  appTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1D1D1F',
    textAlign: 'center',
    marginBottom: 5,
  },
  appVersion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  appDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  settingsContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 15,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  statsContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 15,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1D1D1F',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  infoContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  infoContent: {
    flex: 1,
    marginLeft: 15,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1D1D1F',
  },
  infoValue: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  supportContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    marginBottom: 30,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  supportItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  supportText: {
    flex: 1,
    fontSize: 16,
    color: '#1D1D1F',
    marginLeft: 15,
  },
});

export default SettingsScreen;
