import type { Ski<PERSON>, SkRect, Transforms3d, Vector } from "../../../skia/types";
import { TileMode } from "../../../skia/types";
import type { GradientProps, ImageShaderProps } from "../../types";
export declare const transformOrigin: (origin: Vector, transform: Transforms3d) => (Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "translateX"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "translateY"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "translateZ"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "translate"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "scale"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "scaleX"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "scaleY"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "skewX"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "skewY"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "perspective"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "rotateX"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "rotateY"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "rotateZ"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "rotate"> | Pick<{
    translateX: number;
    translateY: number;
    translateZ: number;
    translate: readonly [number, number] | readonly [number, number, number];
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotateZ: number;
    rotate: number;
    perspective: number;
    rotateX: number;
    rotateY: number;
    matrix: import("../../../skia/types").Matrix4;
}, "matrix">)[];
export declare const processColor: (Skia: Skia, color: number | string | Float32Array | number[]) => import("../../../skia/types").SkColor;
export declare const processGradientProps: (Skia: Skia, { colors, positions, mode, flags, ...transform }: GradientProps) => {
    colors: import("../../../skia/types").SkColor[];
    positions: number[] | null;
    mode: TileMode;
    flags: number | undefined;
    localMatrix: import("../../../skia/types").SkMatrix;
};
export declare const getRect: (Skia: Skia, props: Omit<ImageShaderProps, "tx" | "ty" | "fm" | "mm" | "fit" | "image">) => SkRect | undefined;
