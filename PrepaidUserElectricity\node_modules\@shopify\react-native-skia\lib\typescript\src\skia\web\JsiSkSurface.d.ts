import type { <PERSON>vasKit, Surface } from "canvaskit-wasm";
import type { Sk<PERSON>anvas, SkImage, SkRect, SkSurface } from "../types";
import { HostObject } from "./Host";
import { JsiSkImage } from "./JsiSkImage";
export declare class JsiSkSurface extends HostObject<Surface, "Surface"> implements SkSurface {
    constructor(CanvasKit: CanvasKit, ref: Surface);
    dispose: () => void;
    flush(): void;
    width(): number;
    height(): number;
    getCanvas(): SkCanvas;
    makeImageSnapshot(bounds?: SkRect, outputImage?: JsiSkImage): SkImage;
    getNativeTextureUnstable(): unknown;
}
