{"name": "badgin", "version": "1.2.3", "description": "Badgin makes it easy to subtly notify the user that there is some new activity that might require their attention, or it can be used to indicate a small amount of information, such as an unread count.", "author": "<PERSON>", "homepage": "https://github.com/jaulz/badgin", "main": "build/index.js", "module": "build/index.es.js", "files": ["build"], "types": "build/index.d.ts", "license": "MIT", "scripts": {"test": "", "build": "rm -rf ./build/ && rollup -c", "watch": "concurrently --kill-others \"live-server\" \"rollup -cw\"", "prepublishOnly": "yarn run ci", "ci": "yarn build"}, "repository": {"type": "git", "url": "https://github.com/jaulz/badgin"}, "devDependencies": {"@types/lodash.merge": "^4.6.6", "concurrently": "^5.3.0", "cz-conventional-changelog": "^3.3.0", "husky": "^4.3.0", "live-server": "^1.2.1", "prettier": "^2.1.2", "pretty-quick": "^3.0.2", "rollup": "^2.28.2", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.27.3", "semantic-release": "^17.1.2", "typescript": "^4.0.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "prettier": {"singleQuote": true, "trailingComma": "es5", "semi": false}, "husky": {"hooks": {"post-merge": "yarn install", "post-rewrite": "yarn install", "pre-commit": "pretty-quick --staged --verbose"}}, "dependencies": {}}